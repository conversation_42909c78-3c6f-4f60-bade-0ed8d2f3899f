#!/usr/bin/env ts-node

import * as fs from 'fs';
import * as path from 'path';
import { createMigrationTemplate, generateMigrationFilename } from './migration';

/**
 * Create a new migration file
 */
async function createMigration(): Promise<void> {
  const migrationName = process.argv[2];
  
  if (!migrationName) {
    console.error('❌ Migration name is required');
    console.log('Usage: npm run migrate:create <migration_name>');
    console.log('Example: npm run migrate:create "add user preferences"');
    process.exit(1);
  }
  
  try {
    // Generate migration content
    const migrationContent = createMigrationTemplate(migrationName);
    
    // Generate filename
    const filename = generateMigrationFilename(migrationName);
    const filepath = path.join(__dirname, 'migrations', filename);
    
    // Check if file already exists
    if (fs.existsSync(filepath)) {
      console.error(`❌ Migration file already exists: ${filename}`);
      process.exit(1);
    }
    
    // Create migrations directory if it doesn't exist
    const migrationsDir = path.dirname(filepath);
    if (!fs.existsSync(migrationsDir)) {
      fs.mkdirSync(migrationsDir, { recursive: true });
    }
    
    // Write migration file
    fs.writeFileSync(filepath, migrationContent);
    
    console.log('✅ Migration created successfully!');
    console.log(`📁 File: ${filepath}`);
    console.log('');
    console.log('Next steps:');
    console.log('1. Edit the migration file to add your changes');
    console.log('2. Add the migration to the imports in migrate.ts');
    console.log('3. Run: npm run migrate');
    
  } catch (error) {
    console.error('❌ Failed to create migration:', error);
    process.exit(1);
  }
}

// Run if called directly
if (require.main === module) {
  createMigration().catch((error) => {
    console.error('❌ Unexpected error:', error);
    process.exit(1);
  });
}
