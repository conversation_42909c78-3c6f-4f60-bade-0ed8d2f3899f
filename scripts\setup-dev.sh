#!/bin/bash

# ==============================================
# EIGHTBALL AI - DEVELOPMENT SETUP SCRIPT
# ==============================================

set -e  # Exit on any error

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Function to print colored output
print_status() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

print_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# Function to check if command exists
command_exists() {
    command -v "$1" >/dev/null 2>&1
}

# Check prerequisites
check_prerequisites() {
    print_status "Checking prerequisites..."
    
    if ! command_exists node; then
        print_error "Node.js is not installed. Please install Node.js 18+ first."
        exit 1
    fi
    
    if ! command_exists npm; then
        print_error "npm is not installed. Please install npm first."
        exit 1
    fi
    
    if ! command_exists docker; then
        print_error "Docker is not installed. Please install Docker first."
        exit 1
    fi
    
    if ! command_exists docker-compose; then
        print_error "Docker Compose is not installed. Please install Docker Compose first."
        exit 1
    fi
    
    print_success "All prerequisites are installed"
}

# Setup environment files
setup_environment() {
    print_status "Setting up environment files..."
    
    # Backend environment
    if [ ! -f "backend/.env" ]; then
        cp backend/.env.example backend/.env
        print_success "Created backend/.env from template"
    else
        print_warning "backend/.env already exists, skipping..."
    fi
    
    # Frontend environment
    if [ ! -f "frontend/.env" ]; then
        cp frontend/.env.example frontend/.env
        print_success "Created frontend/.env from template"
    else
        print_warning "frontend/.env already exists, skipping..."
    fi
}

# Install dependencies
install_dependencies() {
    print_status "Installing backend dependencies..."
    cd backend
    npm install
    cd ..
    print_success "Backend dependencies installed"
    
    print_status "Installing frontend dependencies..."
    cd frontend
    npm install
    cd ..
    print_success "Frontend dependencies installed"
}

# Setup Docker services
setup_docker() {
    print_status "Setting up Docker services..."
    
    # Stop any existing containers
    docker-compose down 2>/dev/null || true
    
    # Start database services
    print_status "Starting PostgreSQL and Redis..."
    docker-compose up -d postgres redis
    
    # Wait for services to be ready
    print_status "Waiting for services to be ready..."
    sleep 10
    
    # Check if services are healthy
    if docker-compose ps postgres | grep -q "healthy"; then
        print_success "PostgreSQL is ready"
    else
        print_warning "PostgreSQL might not be fully ready yet"
    fi
    
    if docker-compose ps redis | grep -q "healthy"; then
        print_success "Redis is ready"
    else
        print_warning "Redis might not be fully ready yet"
    fi
}

# Create necessary directories
create_directories() {
    print_status "Creating necessary directories..."
    
    mkdir -p backend/uploads
    mkdir -p backend/logs
    mkdir -p backend/src/database/migrations
    mkdir -p backend/src/database/seeds
    mkdir -p backend/src/types
    mkdir -p backend/src/utils
    mkdir -p backend/src/services
    mkdir -p backend/src/controllers
    mkdir -p backend/src/middleware
    mkdir -p backend/src/models
    mkdir -p backend/src/routes
    mkdir -p backend/src/__tests__
    
    print_success "Directories created"
}

# Build applications
build_applications() {
    print_status "Building applications..."
    
    # Build backend
    cd backend
    npm run build
    cd ..
    print_success "Backend built successfully"
}

# Run tests
run_tests() {
    print_status "Running tests..."
    
    # Backend tests
    cd backend
    npm test 2>/dev/null || print_warning "Backend tests not yet implemented"
    cd ..
    
    # Frontend tests
    cd frontend
    npm test 2>/dev/null || print_warning "Frontend tests not yet implemented"
    cd ..
}

# Main setup function
main() {
    echo "========================================"
    echo "🚀 Eightball AI Development Setup"
    echo "========================================"
    echo ""
    
    check_prerequisites
    setup_environment
    install_dependencies
    create_directories
    setup_docker
    build_applications
    run_tests
    
    echo ""
    echo "========================================"
    print_success "🎉 Development environment setup complete!"
    echo "========================================"
    echo ""
    echo "Next steps:"
    echo "1. Start the development servers:"
    echo "   npm run dev:all"
    echo ""
    echo "2. Or start services individually:"
    echo "   Backend:  cd backend && npm run dev"
    echo "   Frontend: cd frontend && npm run dev"
    echo ""
    echo "3. Access the application:"
    echo "   Frontend: http://localhost:3000"
    echo "   Backend:  http://localhost:3001"
    echo "   API Docs: http://localhost:3001/api"
    echo ""
    echo "4. Optional tools (run with --profile tools):"
    echo "   pgAdmin:        http://localhost:5050"
    echo "   Redis Commander: http://localhost:8081"
    echo ""
    echo "5. To stop all services:"
    echo "   docker-compose down"
    echo ""
}

# Run main function
main "$@"
