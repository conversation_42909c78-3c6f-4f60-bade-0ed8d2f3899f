{"name": "eightball-ai", "version": "1.0.0", "description": "AI-powered educational assistant for Canvas LMS integration", "private": true, "workspaces": ["backend", "frontend"], "scripts": {"setup": "./scripts/setup-dev.sh", "status": "./scripts/check-status.sh", "dev": "concurrently \"npm run dev:backend\" \"npm run dev:frontend\"", "dev:backend": "cd backend && npm run dev", "dev:frontend": "cd frontend && npm run dev", "dev:all": "docker-compose up -d postgres redis && npm run dev", "build": "npm run build:backend && npm run build:frontend", "build:backend": "cd backend && npm run build", "build:frontend": "cd frontend && npm run build", "test": "npm run test:backend && npm run test:frontend", "test:backend": "cd backend && npm test", "test:frontend": "cd frontend && npm test", "lint": "npm run lint:backend && npm run lint:frontend", "lint:backend": "cd backend && npm run lint", "lint:frontend": "cd frontend && npm run lint", "lint:fix": "npm run lint:fix:backend && npm run lint:fix:frontend", "lint:fix:backend": "cd backend && npm run lint:fix", "lint:fix:frontend": "cd frontend && npm run lint:fix", "docker:up": "docker-compose up -d", "docker:down": "docker-compose down", "docker:logs": "docker-compose logs -f", "docker:build": "docker-compose build", "docker:clean": "docker-compose down -v --remove-orphans && docker system prune -f", "db:up": "docker-compose up -d postgres redis", "db:down": "docker-compose stop postgres redis", "tools:up": "docker-compose --profile tools up -d", "tools:down": "docker-compose --profile tools down", "clean": "npm run clean:backend && npm run clean:frontend", "clean:backend": "cd backend && rm -rf dist node_modules", "clean:frontend": "cd frontend && rm -rf dist node_modules", "install:all": "npm install && cd backend && npm install && cd ../frontend && npm install", "postinstall": "echo '🎉 Installation complete! Run npm run setup to configure your development environment.'"}, "devDependencies": {"@types/cookie-parser": "^1.4.9", "concurrently": "^8.2.2"}, "engines": {"node": ">=18.0.0", "npm": ">=8.0.0"}, "keywords": ["ai", "education", "canvas", "lms", "typescript", "react", "express", "postgresql"], "author": "zander-brown", "license": "ISC", "repository": {"type": "git", "url": "https://github.com/your-username/eightball-ai.git"}, "bugs": {"url": "https://github.com/your-username/eightball-ai/issues"}, "homepage": "https://github.com/your-username/eightball-ai#readme", "dependencies": {"cookie-parser": "^1.4.7"}}