# ==============================================
# EIGHTBALL AI - FRONTEND ENVIRONMENT TEMPLATE
# ==============================================

# API Configuration
VITE_API_URL=http://localhost:3001
VITE_API_VERSION=v1

# Application Configuration
VITE_APP_NAME=Eightball AI
VITE_APP_VERSION=1.0.0
VITE_APP_DESCRIPTION=AI-powered educational assistant for Canvas LMS

# Environment
VITE_NODE_ENV=development

# Feature Flags
VITE_ENABLE_DEBUG=true
VITE_ENABLE_MOCK_DATA=false

# Canvas Integration
VITE_CANVAS_OAUTH_URL=http://localhost:3001/auth/canvas

# WebSocket Configuration
VITE_WS_URL=ws://localhost:3001

# File Upload Configuration
VITE_MAX_FILE_SIZE=50MB
VITE_ALLOWED_FILE_TYPES=pdf,docx,pptx,txt,md

# Production overrides (uncomment for production)
# VITE_API_URL=https://api.yourdomain.com
# VITE_NODE_ENV=production
# VITE_ENABLE_DEBUG=false
# VITE_WS_URL=wss://api.yourdomain.com
