import { Pool, PoolClient, QueryResult } from 'pg';
import { createClient, RedisClientType } from 'redis';
import { Pinecone } from '@pinecone-database/pinecone';
import { databaseConfig, redisConfig, vectorDBConfig, connectionString } from './config';

// PostgreSQL connection pool
let pgPool: Pool | null = null;

// Redis client
let redisClient: RedisClientType | null = null;

// Pinecone client
let pineconeClient: Pinecone | null = null;

/**
 * Initialize PostgreSQL connection pool
 */
export const initializeDatabase = async (): Promise<Pool> => {
  if (pgPool) {
    return pgPool;
  }

  try {
    pgPool = new Pool({
      host: databaseConfig.host,
      port: databaseConfig.port,
      database: databaseConfig.database,
      user: databaseConfig.user,
      password: databaseConfig.password,
      ssl: databaseConfig.ssl,
      max: databaseConfig.max,
      idleTimeoutMillis: databaseConfig.idleTimeoutMillis,
      connectionTimeoutMillis: databaseConfig.connectionTimeoutMillis,
    });

    // Test the connection
    const client = await pgPool.connect();
    await client.query('SELECT NOW()');
    client.release();

    console.log('✅ PostgreSQL connection established successfully');
    return pgPool;
  } catch (error) {
    console.error('❌ Failed to connect to PostgreSQL:', error);
    throw error;
  }
};

/**
 * Initialize Redis connection
 */
export const initializeRedis = async (): Promise<RedisClientType> => {
  if (redisClient) {
    return redisClient;
  }

  try {
    const clientOptions: any = {
      socket: {
        host: redisConfig.host,
        port: redisConfig.port,
      },
      database: redisConfig.db,
    };

    if (redisConfig.password) {
      clientOptions.password = redisConfig.password;
    }

    redisClient = createClient(clientOptions);

    redisClient.on('error', (err) => {
      console.error('Redis Client Error:', err);
    });

    redisClient.on('connect', () => {
      console.log('✅ Redis connection established successfully');
    });

    redisClient.on('disconnect', () => {
      console.log('⚠️ Redis disconnected');
    });

    await redisClient.connect();
    return redisClient;
  } catch (error) {
    console.error('❌ Failed to connect to Redis:', error);
    throw error;
  }
};

/**
 * Initialize Pinecone connection
 */
export const initializePinecone = async (): Promise<Pinecone | null> => {
  if (!vectorDBConfig) {
    console.log('⚠️ Vector database configuration not provided, skipping Pinecone initialization');
    return null;
  }

  if (pineconeClient) {
    return pineconeClient;
  }

  try {
    pineconeClient = new Pinecone({
      apiKey: vectorDBConfig.apiKey,
    });

    // Test the connection by listing indexes
    const indexes = await pineconeClient.listIndexes();
    console.log('✅ Pinecone connection established successfully');
    console.log(`📊 Available indexes: ${indexes.indexes?.map(i => i.name).join(', ') || 'none'}`);

    return pineconeClient;
  } catch (error) {
    console.error('❌ Failed to connect to Pinecone:', error);
    console.log('⚠️ Vector database features will be disabled');
    return null;
  }
};

/**
 * Get PostgreSQL pool instance
 */
export const getDatabase = (): Pool => {
  if (!pgPool) {
    throw new Error('Database not initialized. Call initializeDatabase() first.');
  }
  return pgPool;
};

/**
 * Get Redis client instance
 */
export const getRedis = (): RedisClientType => {
  if (!redisClient) {
    throw new Error('Redis not initialized. Call initializeRedis() first.');
  }
  return redisClient;
};

/**
 * Get Pinecone client instance
 */
export const getPinecone = (): Pinecone | null => {
  return pineconeClient;
};

/**
 * Execute a database query with error handling
 */
export const query = async (text: string, params?: any[]): Promise<QueryResult> => {
  const pool = getDatabase();
  const start = Date.now();
  
  try {
    const result = await pool.query(text, params);
    const duration = Date.now() - start;
    
    if (process.env.NODE_ENV === 'development') {
      console.log('🔍 Executed query', { text, duration, rows: result.rowCount });
    }
    
    return result;
  } catch (error) {
    console.error('❌ Database query error:', error);
    throw error;
  }
};

/**
 * Execute a query with a specific client (for transactions)
 */
export const queryWithClient = async (
  client: PoolClient,
  text: string,
  params?: any[]
): Promise<QueryResult> => {
  const start = Date.now();
  
  try {
    const result = await client.query(text, params);
    const duration = Date.now() - start;
    
    if (process.env.NODE_ENV === 'development') {
      console.log('🔍 Executed query with client', { text, duration, rows: result.rowCount });
    }
    
    return result;
  } catch (error) {
    console.error('❌ Database query error:', error);
    throw error;
  }
};

/**
 * Execute a transaction
 */
export const transaction = async <T>(
  callback: (client: PoolClient) => Promise<T>
): Promise<T> => {
  const pool = getDatabase();
  const client = await pool.connect();
  
  try {
    await client.query('BEGIN');
    const result = await callback(client);
    await client.query('COMMIT');
    return result;
  } catch (error) {
    await client.query('ROLLBACK');
    throw error;
  } finally {
    client.release();
  }
};

/**
 * Close all database connections
 */
export const closeConnections = async (): Promise<void> => {
  const promises: Promise<void>[] = [];

  if (pgPool) {
    promises.push(pgPool.end());
    pgPool = null;
  }

  if (redisClient) {
    promises.push(redisClient.quit().then(() => {}));
    redisClient = null;
  }

  // Pinecone doesn't need explicit closing
  pineconeClient = null;

  await Promise.all(promises);
  console.log('✅ All database connections closed');
};

/**
 * Initialize all database connections
 */
export const initializeAllConnections = async (): Promise<void> => {
  console.log('🚀 Initializing database connections...');
  
  await initializeDatabase();
  await initializeRedis();
  await initializePinecone();
  
  console.log('✅ All database connections initialized successfully');
};

// Graceful shutdown handling
process.on('SIGINT', async () => {
  console.log('🛑 Received SIGINT, closing database connections...');
  await closeConnections();
  process.exit(0);
});

process.on('SIGTERM', async () => {
  console.log('🛑 Received SIGTERM, closing database connections...');
  await closeConnections();
  process.exit(0);
});
