#!/usr/bin/env ts-node

import { initializeDatabase, transaction, closeConnections } from './connection';
import {
  Migration,
  createMigrationsTable,
  getExecutedMigrations,
  getMigrationsToRun,
  getMigrationsToRollback,
  getNextBatch,
  executeMigrationUp,
  executeMigrationDown,
  validateMigrations,
} from './migration';

// Import all migrations
import { migration as initialSchema } from './migrations/001_initial_schema';

// List of all migrations in order
const allMigrations: Migration[] = [
  initialSchema,
];

/**
 * Run pending migrations
 */
async function runMigrations(): Promise<void> {
  console.log('🚀 Starting database migrations...');
  
  try {
    // Validate migrations
    validateMigrations(allMigrations);
    
    await transaction(async (client) => {
      // Ensure migrations table exists
      await createMigrationsTable(client);
      
      // Get executed migrations
      const executedMigrations = await getExecutedMigrations(client);
      console.log(`📊 Found ${executedMigrations.length} executed migrations`);
      
      // Get migrations to run
      const migrationsToRun = getMigrationsToRun(allMigrations, executedMigrations);
      
      if (migrationsToRun.length === 0) {
        console.log('✅ No pending migrations to run');
        return;
      }
      
      console.log(`📋 Found ${migrationsToRun.length} pending migrations`);
      
      // Get next batch number
      const batch = await getNextBatch(client);
      console.log(`📦 Running migrations in batch ${batch}`);
      
      // Run migrations
      for (const migration of migrationsToRun) {
        await executeMigrationUp(client, migration, batch);
      }
      
      console.log(`✅ Successfully ran ${migrationsToRun.length} migrations`);
    });
    
  } catch (error) {
    console.error('❌ Migration failed:', error);
    process.exit(1);
  }
}

/**
 * Rollback latest batch of migrations
 */
async function rollbackMigrations(): Promise<void> {
  console.log('🔄 Starting migration rollback...');
  
  try {
    await transaction(async (client) => {
      // Ensure migrations table exists
      await createMigrationsTable(client);
      
      // Get migrations to rollback
      const migrationsToRollback = await getMigrationsToRollback(client, allMigrations);
      
      if (migrationsToRollback.length === 0) {
        console.log('✅ No migrations to rollback');
        return;
      }
      
      console.log(`📋 Found ${migrationsToRollback.length} migrations to rollback`);
      
      // Rollback migrations in reverse order
      for (const migration of migrationsToRollback) {
        await executeMigrationDown(client, migration);
      }
      
      console.log(`✅ Successfully rolled back ${migrationsToRollback.length} migrations`);
    });
    
  } catch (error) {
    console.error('❌ Migration rollback failed:', error);
    process.exit(1);
  }
}

/**
 * Show migration status
 */
async function showStatus(): Promise<void> {
  console.log('📊 Migration Status');
  console.log('==================');
  
  try {
    await transaction(async (client) => {
      // Ensure migrations table exists
      await createMigrationsTable(client);
      
      // Get executed migrations
      const executedMigrations = await getExecutedMigrations(client);
      const migrationsToRun = getMigrationsToRun(allMigrations, executedMigrations);
      
      console.log(`Total migrations: ${allMigrations.length}`);
      console.log(`Executed: ${executedMigrations.length}`);
      console.log(`Pending: ${migrationsToRun.length}`);
      console.log('');
      
      if (executedMigrations.length > 0) {
        console.log('Executed migrations:');
        for (const migration of executedMigrations) {
          console.log(`  ✅ ${migration.id} - ${migration.name} (batch ${migration.batch})`);
        }
        console.log('');
      }
      
      if (migrationsToRun.length > 0) {
        console.log('Pending migrations:');
        for (const migration of migrationsToRun) {
          console.log(`  ⏳ ${migration.id} - ${migration.name}`);
        }
      }
    });
    
  } catch (error) {
    console.error('❌ Failed to get migration status:', error);
    process.exit(1);
  }
}

/**
 * Reset database (rollback all migrations)
 */
async function resetDatabase(): Promise<void> {
  console.log('🔄 Resetting database (rolling back all migrations)...');
  
  try {
    await transaction(async (client) => {
      // Ensure migrations table exists
      await createMigrationsTable(client);
      
      // Get all executed migrations
      const executedMigrations = await getExecutedMigrations(client);
      
      if (executedMigrations.length === 0) {
        console.log('✅ No migrations to rollback');
        return;
      }
      
      console.log(`📋 Rolling back ${executedMigrations.length} migrations`);
      
      // Get migrations in reverse order
      const migrationMap = new Map(allMigrations.map(m => [m.id, m]));
      const migrationsToRollback = executedMigrations
        .reverse()
        .map(em => migrationMap.get(em.id))
        .filter((m): m is Migration => m !== undefined);
      
      // Rollback all migrations
      for (const migration of migrationsToRollback) {
        await executeMigrationDown(client, migration);
      }
      
      console.log(`✅ Successfully reset database`);
    });
    
  } catch (error) {
    console.error('❌ Database reset failed:', error);
    process.exit(1);
  }
}

/**
 * Main function
 */
async function main(): Promise<void> {
  const command = process.argv[2] || 'up';
  
  try {
    // Initialize database connection
    await initializeDatabase();
    
    switch (command) {
      case 'up':
        await runMigrations();
        break;
      case 'down':
        await rollbackMigrations();
        break;
      case 'status':
        await showStatus();
        break;
      case 'reset':
        await resetDatabase();
        break;
      default:
        console.log('Usage: npm run migrate [up|down|status|reset]');
        console.log('  up     - Run pending migrations (default)');
        console.log('  down   - Rollback latest batch of migrations');
        console.log('  status - Show migration status');
        console.log('  reset  - Rollback all migrations');
        process.exit(1);
    }
    
  } catch (error) {
    console.error('❌ Migration command failed:', error);
    process.exit(1);
  } finally {
    await closeConnections();
  }
}

// Run if called directly
if (require.main === module) {
  main().catch((error) => {
    console.error('❌ Unexpected error:', error);
    process.exit(1);
  });
}
