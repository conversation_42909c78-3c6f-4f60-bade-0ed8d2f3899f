import { Router, Request, Response } from 'express';
import {
  registerUser,
  loginUser,
  refreshAccessToken,
  logoutUser,
  logoutUserFromAllDevices,
  getCurrentUser,
  updateUserProfile,
  changePassword,
} from '../auth/service';
import { authenticate, AuthenticatedRequest } from '../middleware/auth';
import { AUTH_CONSTANTS } from '../auth/config';

const router = Router();

/**
 * POST /api/auth/register
 * Register a new user
 */
router.post('/register', async (req: Request, res: Response): Promise<void> => {
  try {
    const { email, password, firstName, lastName } = req.body;
    
    const result = await registerUser(
      { email, password, firstName, lastName },
      req.ip,
      req.get('User-Agent')
    );

    // Set refresh token as HTTP-only cookie
    res.cookie(AUTH_CONSTANTS.REFRESH_COOKIE_NAME, result.refreshToken, {
      httpOnly: true,
      secure: process.env.NODE_ENV === 'production',
      sameSite: 'lax',
      maxAge: result.refreshExpiresIn * 1000,
    });

    res.status(201).json({
      message: 'User registered successfully',
      user: result.user,
      accessToken: result.accessToken,
      expiresIn: result.expiresIn,
    });
  } catch (error) {
    console.error('Registration error:', error);
    res.status(400).json({
      error: 'Registration Failed',
      message: error instanceof Error ? error.message : 'Registration failed',
    });
  }
});

/**
 * POST /api/auth/login
 * Login user
 */
router.post('/login', async (req: Request, res: Response): Promise<void> => {
  try {
    const { email, password } = req.body;
    
    const result = await loginUser(
      { email, password },
      req.ip,
      req.get('User-Agent')
    );

    // Set refresh token as HTTP-only cookie
    res.cookie(AUTH_CONSTANTS.REFRESH_COOKIE_NAME, result.refreshToken, {
      httpOnly: true,
      secure: process.env.NODE_ENV === 'production',
      sameSite: 'lax',
      maxAge: result.refreshExpiresIn * 1000,
    });

    res.json({
      message: 'Login successful',
      user: result.user,
      accessToken: result.accessToken,
      expiresIn: result.expiresIn,
    });
  } catch (error) {
    console.error('Login error:', error);
    res.status(401).json({
      error: 'Login Failed',
      message: error instanceof Error ? error.message : 'Login failed',
    });
  }
});

/**
 * POST /api/auth/refresh
 * Refresh access token
 */
router.post('/refresh', async (req: Request, res: Response): Promise<void> => {
  try {
    const refreshToken = req.cookies[AUTH_CONSTANTS.REFRESH_COOKIE_NAME] || req.body.refreshToken;
    
    if (!refreshToken) {
      res.status(401).json({
        error: 'Unauthorized',
        message: 'Refresh token not provided',
      });
      return;
    }

    const result = await refreshAccessToken(
      { refreshToken },
      req.ip,
      req.get('User-Agent')
    );

    // Set new refresh token as HTTP-only cookie
    res.cookie(AUTH_CONSTANTS.REFRESH_COOKIE_NAME, result.refreshToken, {
      httpOnly: true,
      secure: process.env.NODE_ENV === 'production',
      sameSite: 'lax',
      maxAge: result.refreshExpiresIn * 1000,
    });

    res.json({
      message: 'Token refreshed successfully',
      user: result.user,
      accessToken: result.accessToken,
      expiresIn: result.expiresIn,
    });
  } catch (error) {
    console.error('Token refresh error:', error);
    res.status(401).json({
      error: 'Token Refresh Failed',
      message: error instanceof Error ? error.message : 'Token refresh failed',
    });
  }
});

/**
 * POST /api/auth/logout
 * Logout user (current session)
 */
router.post('/logout', authenticate, async (req: AuthenticatedRequest, res: Response): Promise<void> => {
  try {
    await logoutUser(req.sessionId);

    // Clear refresh token cookie
    res.clearCookie(AUTH_CONSTANTS.REFRESH_COOKIE_NAME);

    res.json({
      message: 'Logout successful',
    });
  } catch (error) {
    console.error('Logout error:', error);
    res.status(500).json({
      error: 'Logout Failed',
      message: 'Logout failed',
    });
  }
});

/**
 * POST /api/auth/logout-all
 * Logout user from all devices
 */
router.post('/logout-all', authenticate, async (req: AuthenticatedRequest, res: Response): Promise<void> => {
  try {
    await logoutUserFromAllDevices(req.user.id);

    // Clear refresh token cookie
    res.clearCookie(AUTH_CONSTANTS.REFRESH_COOKIE_NAME);

    res.json({
      message: 'Logged out from all devices successfully',
    });
  } catch (error) {
    console.error('Logout all error:', error);
    res.status(500).json({
      error: 'Logout Failed',
      message: 'Logout from all devices failed',
    });
  }
});

/**
 * GET /api/auth/me
 * Get current user profile
 */
router.get('/me', authenticate, async (req: AuthenticatedRequest, res: Response): Promise<void> => {
  try {
    const user = await getCurrentUser(req.user.id);

    res.json({
      user,
    });
  } catch (error) {
    console.error('Get current user error:', error);
    res.status(500).json({
      error: 'Failed to get user profile',
      message: 'Could not retrieve user profile',
    });
  }
});

/**
 * PUT /api/auth/profile
 * Update user profile
 */
router.put('/profile', authenticate, async (req: AuthenticatedRequest, res: Response): Promise<void> => {
  try {
    const { firstName, lastName, preferences } = req.body;
    
    const updates: any = {};
    if (firstName !== undefined) updates.firstName = firstName;
    if (lastName !== undefined) updates.lastName = lastName;
    if (preferences !== undefined) updates.preferences = preferences;

    const user = await updateUserProfile(req.user.id, updates);

    res.json({
      message: 'Profile updated successfully',
      user,
    });
  } catch (error) {
    console.error('Update profile error:', error);
    res.status(400).json({
      error: 'Profile Update Failed',
      message: error instanceof Error ? error.message : 'Profile update failed',
    });
  }
});

/**
 * PUT /api/auth/password
 * Change user password
 */
router.put('/password', authenticate, async (req: AuthenticatedRequest, res: Response): Promise<void> => {
  try {
    const { currentPassword, newPassword } = req.body;
    
    if (!currentPassword || !newPassword) {
      res.status(400).json({
        error: 'Bad Request',
        message: 'Current password and new password are required',
      });
      return;
    }

    await changePassword(req.user.id, currentPassword, newPassword);

    res.json({
      message: 'Password changed successfully',
    });
  } catch (error) {
    console.error('Change password error:', error);
    res.status(400).json({
      error: 'Password Change Failed',
      message: error instanceof Error ? error.message : 'Password change failed',
    });
  }
});

/**
 * GET /api/auth/status
 * Check authentication status
 */
router.get('/status', authenticate, async (req: AuthenticatedRequest, res: Response): Promise<void> => {
  res.json({
    authenticated: true,
    user: {
      id: req.user.id,
      email: req.user.email,
      firstName: req.user.firstName,
      lastName: req.user.lastName,
      emailVerified: req.user.emailVerified,
      isActive: req.user.isActive,
    },
  });
});

export default router;
