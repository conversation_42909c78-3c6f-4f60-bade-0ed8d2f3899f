export interface User {
  id: string;
  email: string;
  firstName: string;
  lastName: string;
  canvasUserId?: string;
  isActive: boolean;
  emailVerified: boolean;
  lastLoginAt?: string;
  preferences?: Record<string, any>;
  createdAt: string;
  updatedAt: string;
}

export interface AuthUser {
  id: string;
  email: string;
  firstName: string;
  lastName: string;
  emailVerified: boolean;
  isActive: boolean;
}

export interface UserProfile {
  firstName: string;
  lastName: string;
  preferences?: Record<string, any>;
}

export interface PasswordChange {
  currentPassword: string;
  newPassword: string;
  confirmPassword: string;
}
