# ==============================================
# EIGHTBALL AI - ENVIRONMENT CONFIGURATION
# ==============================================

# Server Configuration
PORT=3001
NODE_ENV=development
API_VERSION=v1

# Database Configuration
DATABASE_URL=postgresql://eightball_user:eightball_password@localhost:5432/eightball_ai_dev
DATABASE_HOST=localhost
DATABASE_PORT=5432
DATABASE_NAME=eightball_ai_dev
DATABASE_USER=eightball_user
DATABASE_PASSWORD=eightball_password

# Redis Configuration
REDIS_URL=redis://localhost:6379
REDIS_HOST=localhost
REDIS_PORT=6379
REDIS_PASSWORD=

# JWT Configuration
JWT_SECRET=dev-jwt-secret-key-change-in-production-8ball-ai-2025
JWT_EXPIRES_IN=7d
JWT_REFRESH_EXPIRES_IN=30d

# Canvas API Configuration
CANVAS_API_URL=https://your-institution.instructure.com/api/v1
CANVAS_CLIENT_ID=your-canvas-client-id
CANVAS_CLIENT_SECRET=your-canvas-client-secret
CANVAS_REDIRECT_URI=http://localhost:3000/auth/canvas/callback

# OpenAI Configuration
OPENAI_API_KEY=your-openai-api-key
OPENAI_MODEL=gpt-4
OPENAI_MAX_TOKENS=4000

# Anthropic Configuration (alternative to OpenAI)
ANTHROPIC_API_KEY=your-anthropic-api-key
ANTHROPIC_MODEL=claude-3-sonnet-20240229

# Vector Database Configuration (Pinecone example)
PINECONE_API_KEY=your-pinecone-api-key
PINECONE_ENVIRONMENT=your-pinecone-environment
PINECONE_INDEX_NAME=eightball-ai-embeddings

# File Upload Configuration
MAX_FILE_SIZE=50MB
UPLOAD_DIR=./uploads
ALLOWED_FILE_TYPES=pdf,docx,pptx,txt,md

# Rate Limiting
RATE_LIMIT_WINDOW_MS=900000
RATE_LIMIT_MAX_REQUESTS=100

# CORS Configuration
CORS_ORIGIN=http://localhost:3000

# Logging Configuration
LOG_LEVEL=debug
LOG_FORMAT=combined

# Session Configuration
SESSION_SECRET=dev-session-secret-change-in-production
SESSION_MAX_AGE=86400000

# Development Configuration
ENABLE_SWAGGER=true
ENABLE_DEBUG_ROUTES=true
