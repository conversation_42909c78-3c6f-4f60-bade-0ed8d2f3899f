import { PoolClient } from 'pg';
import { queryWithClient } from './connection';

export interface Migration {
  id: string;
  name: string;
  up: (client: PoolClient) => Promise<void>;
  down: (client: PoolClient) => Promise<void>;
}

export interface MigrationRecord {
  id: string;
  name: string;
  executed_at: Date;
  batch: number;
}

/**
 * Create migrations table if it doesn't exist
 */
export const createMigrationsTable = async (client: PoolClient): Promise<void> => {
  await queryWithClient(client, `
    CREATE TABLE IF NOT EXISTS migrations (
      id VARCHAR(255) PRIMARY KEY,
      name VARCHAR(255) NOT NULL,
      executed_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
      batch INTEGER NOT NULL
    );
  `);
};

/**
 * Get executed migrations
 */
export const getExecutedMigrations = async (client: PoolClient): Promise<MigrationRecord[]> => {
  const result = await queryWithClient(client, `
    SELECT id, name, executed_at, batch
    FROM migrations
    ORDER BY executed_at ASC
  `);
  
  return result.rows.map(row => ({
    id: row.id,
    name: row.name,
    executed_at: row.executed_at,
    batch: row.batch,
  }));
};

/**
 * Get the next batch number
 */
export const getNextBatch = async (client: PoolClient): Promise<number> => {
  const result = await queryWithClient(client, `
    SELECT COALESCE(MAX(batch), 0) + 1 as next_batch
    FROM migrations
  `);
  
  return result.rows[0].next_batch;
};

/**
 * Record migration execution
 */
export const recordMigration = async (
  client: PoolClient,
  migration: Migration,
  batch: number
): Promise<void> => {
  await queryWithClient(client, `
    INSERT INTO migrations (id, name, batch)
    VALUES ($1, $2, $3)
  `, [migration.id, migration.name, batch]);
};

/**
 * Remove migration record
 */
export const removeMigrationRecord = async (
  client: PoolClient,
  migrationId: string
): Promise<void> => {
  await queryWithClient(client, `
    DELETE FROM migrations WHERE id = $1
  `, [migrationId]);
};

/**
 * Get migrations to run
 */
export const getMigrationsToRun = (
  allMigrations: Migration[],
  executedMigrations: MigrationRecord[]
): Migration[] => {
  const executedIds = new Set(executedMigrations.map(m => m.id));
  return allMigrations.filter(m => !executedIds.has(m.id));
};

/**
 * Get migrations to rollback (from latest batch)
 */
export const getMigrationsToRollback = async (
  client: PoolClient,
  allMigrations: Migration[]
): Promise<Migration[]> => {
  const result = await queryWithClient(client, `
    SELECT id FROM migrations
    WHERE batch = (SELECT MAX(batch) FROM migrations)
    ORDER BY executed_at DESC
  `);
  
  const latestBatchIds = result.rows.map(row => row.id);
  const migrationMap = new Map(allMigrations.map(m => [m.id, m]));
  
  return latestBatchIds
    .map(id => migrationMap.get(id))
    .filter((m): m is Migration => m !== undefined);
};

/**
 * Execute migration up
 */
export const executeMigrationUp = async (
  client: PoolClient,
  migration: Migration,
  batch: number
): Promise<void> => {
  console.log(`⬆️  Running migration: ${migration.name}`);
  
  try {
    await migration.up(client);
    await recordMigration(client, migration, batch);
    console.log(`✅ Migration completed: ${migration.name}`);
  } catch (error) {
    console.error(`❌ Migration failed: ${migration.name}`, error);
    throw error;
  }
};

/**
 * Execute migration down
 */
export const executeMigrationDown = async (
  client: PoolClient,
  migration: Migration
): Promise<void> => {
  console.log(`⬇️  Rolling back migration: ${migration.name}`);
  
  try {
    await migration.down(client);
    await removeMigrationRecord(client, migration.id);
    console.log(`✅ Migration rolled back: ${migration.name}`);
  } catch (error) {
    console.error(`❌ Migration rollback failed: ${migration.name}`, error);
    throw error;
  }
};

/**
 * Validate migration structure
 */
export const validateMigration = (migration: Migration): void => {
  if (!migration.id || typeof migration.id !== 'string') {
    throw new Error('Migration must have a valid id');
  }
  
  if (!migration.name || typeof migration.name !== 'string') {
    throw new Error('Migration must have a valid name');
  }
  
  if (typeof migration.up !== 'function') {
    throw new Error('Migration must have an up function');
  }
  
  if (typeof migration.down !== 'function') {
    throw new Error('Migration must have a down function');
  }
};

/**
 * Validate migrations array
 */
export const validateMigrations = (migrations: Migration[]): void => {
  const ids = new Set<string>();
  
  for (const migration of migrations) {
    validateMigration(migration);
    
    if (ids.has(migration.id)) {
      throw new Error(`Duplicate migration id: ${migration.id}`);
    }
    
    ids.add(migration.id);
  }
};

/**
 * Create a new migration template
 */
export const createMigrationTemplate = (name: string): string => {
  const timestamp = new Date().toISOString().replace(/[-:]/g, '').replace(/\..+/, '');
  const id = `${timestamp}_${name.toLowerCase().replace(/\s+/g, '_')}`;
  
  return `import { PoolClient } from 'pg';
import { Migration } from '../migration';
import { queryWithClient } from '../connection';

export const migration: Migration = {
  id: '${id}',
  name: '${name}',
  
  async up(client: PoolClient): Promise<void> {
    // Add your migration logic here
    await queryWithClient(client, \`
      -- Your SQL here
    \`);
  },
  
  async down(client: PoolClient): Promise<void> {
    // Add your rollback logic here
    await queryWithClient(client, \`
      -- Your rollback SQL here
    \`);
  },
};
`;
};

/**
 * Generate migration filename
 */
export const generateMigrationFilename = (name: string): string => {
  const timestamp = new Date().toISOString().replace(/[-:]/g, '').replace(/\..+/, '');
  const cleanName = name.toLowerCase().replace(/\s+/g, '_');
  return `${timestamp}_${cleanName}.ts`;
};
