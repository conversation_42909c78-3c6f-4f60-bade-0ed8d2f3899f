import { z } from 'zod';
import dotenv from 'dotenv';

// Load environment variables
dotenv.config();

// Database configuration schema
const DatabaseConfigSchema = z.object({
  host: z.string().default('localhost'),
  port: z.number().default(5432),
  database: z.string(),
  user: z.string(),
  password: z.string(),
  ssl: z.boolean().default(false),
  max: z.number().default(20),
  idleTimeoutMillis: z.number().default(30000),
  connectionTimeoutMillis: z.number().default(2000),
});

// Redis configuration schema
const RedisConfigSchema = z.object({
  host: z.string().default('localhost'),
  port: z.number().default(6379),
  password: z.string().optional(),
  db: z.number().default(0),
  retryDelayOnFailover: z.number().default(100),
  maxRetriesPerRequest: z.number().default(3),
});

// Vector database configuration schema
const VectorDBConfigSchema = z.object({
  provider: z.enum(['pinecone', 'weaviate']).default('pinecone'),
  apiKey: z.string(),
  environment: z.string().optional(),
  indexName: z.string(),
  dimension: z.number().default(1536), // OpenAI embedding dimension
});

export type DatabaseConfig = z.infer<typeof DatabaseConfigSchema>;
export type RedisConfig = z.infer<typeof RedisConfigSchema>;
export type VectorDBConfig = z.infer<typeof VectorDBConfigSchema>;

// Parse and validate database configuration
export const databaseConfig: DatabaseConfig = DatabaseConfigSchema.parse({
  host: process.env.DATABASE_HOST,
  port: parseInt(process.env.DATABASE_PORT || '5432'),
  database: process.env.DATABASE_NAME,
  user: process.env.DATABASE_USER,
  password: process.env.DATABASE_PASSWORD,
  ssl: process.env.NODE_ENV === 'production',
  max: parseInt(process.env.DATABASE_MAX_CONNECTIONS || '20'),
  idleTimeoutMillis: parseInt(process.env.DATABASE_IDLE_TIMEOUT || '30000'),
  connectionTimeoutMillis: parseInt(process.env.DATABASE_CONNECTION_TIMEOUT || '2000'),
});

// Parse and validate Redis configuration
export const redisConfig: RedisConfig = RedisConfigSchema.parse({
  host: process.env.REDIS_HOST,
  port: parseInt(process.env.REDIS_PORT || '6379'),
  password: process.env.REDIS_PASSWORD || undefined,
  db: parseInt(process.env.REDIS_DB || '0'),
  retryDelayOnFailover: parseInt(process.env.REDIS_RETRY_DELAY || '100'),
  maxRetriesPerRequest: parseInt(process.env.REDIS_MAX_RETRIES || '3'),
});

// Parse and validate vector database configuration
export const vectorDBConfig: VectorDBConfig | null = (() => {
  try {
    return VectorDBConfigSchema.parse({
      provider: process.env.VECTOR_DB_PROVIDER || 'pinecone',
      apiKey: process.env.PINECONE_API_KEY,
      environment: process.env.PINECONE_ENVIRONMENT,
      indexName: process.env.PINECONE_INDEX_NAME,
      dimension: parseInt(process.env.VECTOR_DIMENSION || '1536'),
    });
  } catch (error) {
    console.warn('Vector database configuration not provided or invalid. Vector features will be disabled.');
    return null;
  }
})();

// Connection string for PostgreSQL
export const connectionString = `postgresql://${databaseConfig.user}:${databaseConfig.password}@${databaseConfig.host}:${databaseConfig.port}/${databaseConfig.database}`;

// Redis connection string
export const redisConnectionString = `redis://${redisConfig.password ? `:${redisConfig.password}@` : ''}${redisConfig.host}:${redisConfig.port}/${redisConfig.db}`;

// Export configuration object
export const config = {
  database: databaseConfig,
  redis: redisConfig,
  vectorDB: vectorDBConfig,
  connectionString,
  redisConnectionString,
} as const;
