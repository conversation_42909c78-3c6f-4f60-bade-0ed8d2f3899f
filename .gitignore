# ==============================================
# EIGHTBALL AI - GIT IGNORE CONFIGURATION
# ==============================================

# Environment files
.env
.env.local
.env.development.local
.env.test.local
.env.production.local

# Dependencies
node_modules/
npm-debug.log*
yarn-debug.log*
yarn-error.log*
pnpm-debug.log*

# Build outputs
dist/
build/
*.tsbuildinfo

# Logs
logs/
*.log
npm-debug.log*
yarn-debug.log*
yarn-error.log*
lerna-debug.log*
.pnpm-debug.log*

# Runtime data
pids/
*.pid
*.seed
*.pid.lock

# Coverage directory used by tools like istanbul
coverage/
*.lcov

# nyc test coverage
.nyc_output/

# Dependency directories
node_modules/
jspm_packages/

# Snowpack dependency directory (https://snowpack.dev/)
web_modules/

# TypeScript cache
*.tsbuildinfo

# Optional npm cache directory
.npm

# Optional eslint cache
.eslintcache

# Optional stylelint cache
.stylelintcache

# Microbundle cache
.rpt2_cache/
.rts2_cache_cjs/
.rts2_cache_es/
.rts2_cache_umd/

# Optional REPL history
.node_repl_history

# Output of 'npm pack'
*.tgz

# Yarn Integrity file
.yarn-integrity

# parcel-bundler cache (https://parceljs.org/)
.cache/
.parcel-cache/

# Next.js build output
.next/
out/

# Nuxt.js build / generate output
.nuxt/
dist/

# Gatsby files
.cache/
public/

# Vite build output
dist/
dist-ssr/
*.local

# Rollup cache
.rollup.cache/

# Serverless directories
.serverless/

# FuseBox cache
.fusebox/

# DynamoDB Local files
.dynamodb/

# TernJS port file
.tern-port

# Stores VSCode versions used for testing VSCode extensions
.vscode-test/

# yarn v2
.yarn/cache
.yarn/unplugged
.yarn/build-state.yml
.yarn/install-state.gz
.pnp.*

# Editor directories and files
.vscode/
.idea/
*.swp
*.swo
*~

# OS generated files
.DS_Store
.DS_Store?
._*
.Spotlight-V100
.Trashes
ehthumbs.db
Thumbs.db

# Temporary folders
tmp/
temp/

# File uploads
uploads/
*.upload

# Database files
*.sqlite
*.sqlite3
*.db

# Docker
.docker/

# Application specific
backend/uploads/
backend/logs/
frontend/dist/

# Testing
coverage/
.jest/
test-results/
playwright-report/

# Documentation
docs/build/

# Backup files
*.backup
*.bak
*.orig

# Lock files (keep package-lock.json but ignore others)
yarn.lock
pnpm-lock.yaml

# IDE specific
.vscode/settings.json
.vscode/launch.json
.vscode/extensions.json
*.code-workspace

# Local development
.local/
local/

# Certificates
*.pem
*.key
*.crt
*.cert

# API keys and secrets (double check these are not committed)
secrets/
.secrets/
api-keys.json
