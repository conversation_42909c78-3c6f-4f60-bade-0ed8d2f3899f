import React from 'react';
import {
  Container,
  Typography,
  Box,
  Paper,
  Button,
  Grid,
} from '@mui/material';
import { Assignment, Add } from '@mui/icons-material';

const Exams: React.FC = () => {
  return (
    <Container maxWidth="lg">
      <Box sx={{ mb: 4 }}>
        <Typography variant="h4" component="h1" gutterBottom>
          Practice Exams
        </Typography>
        <Typography variant="subtitle1" color="text.secondary">
          AI-generated practice exams based on your course content
        </Typography>
      </Box>

      <Grid container spacing={3}>
        <Grid item xs={12}>
          <Paper sx={{ p: 4, textAlign: 'center' }}>
            <Assignment sx={{ fontSize: 64, color: 'text.secondary', mb: 2 }} />
            <Typography variant="h6" gutterBottom>
              Practice Exams Feature Coming Soon
            </Typography>
            <Typography variant="body1" color="text.secondary" sx={{ mb: 3 }}>
              This feature will allow you to generate and take practice exams based on your course materials.
            </Typography>
            <Button variant="contained" startIcon={<Add />} disabled>
              Create Practice Exam
            </Button>
          </Paper>
        </Grid>
      </Grid>
    </Container>
  );
};

export default Exams;
