import { PoolClient } from 'pg';
import { Migration } from '../migration';
import { queryWithClient } from '../connection';

export const migration: Migration = {
  id: '001_initial_schema',
  name: 'Create initial database schema',
  
  async up(client: PoolClient): Promise<void> {
    // Create extensions
    await queryWithClient(client, `
      CREATE EXTENSION IF NOT EXISTS "uuid-ossp";
      CREATE EXTENSION IF NOT EXISTS "pgcrypto";
    `);

    // Create schemas
    await queryWithClient(client, `
      CREATE SCHEMA IF NOT EXISTS auth;
      CREATE SCHEMA IF NOT EXISTS app;
      CREATE SCHEMA IF NOT EXISTS analytics;
    `);

    // Create updated_at trigger function
    await queryWithClient(client, `
      CREATE OR REPLACE FUNCTION update_updated_at_column()
      RETURNS TRIGGER AS $$
      BEGIN
          NEW.updated_at = NOW();
          RETURN NEW;
      END;
      $$ language 'plpgsql';
    `);

    // Create auth.users table
    await queryWith<PERSON>lient(client, `
      CREATE TABLE auth.users (
        id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
        email VARCHAR(255) UNIQUE NOT NULL,
        password_hash VARCHAR(255),
        first_name VARCHAR(100) NOT NULL,
        last_name VARCHAR(100) NOT NULL,
        canvas_user_id VARCHAR(100),
        canvas_access_token TEXT,
        is_active BOOLEAN DEFAULT true,
        email_verified BOOLEAN DEFAULT false,
        last_login_at TIMESTAMP WITH TIME ZONE,
        preferences JSONB DEFAULT '{}',
        created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
        updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
      );
    `);

    // Create auth.sessions table
    await queryWithClient(client, `
      CREATE TABLE auth.sessions (
        id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
        user_id UUID NOT NULL REFERENCES auth.users(id) ON DELETE CASCADE,
        token_hash VARCHAR(255) NOT NULL,
        expires_at TIMESTAMP WITH TIME ZONE NOT NULL,
        ip_address INET,
        user_agent TEXT,
        created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
        updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
      );
    `);

    // Create app.courses table
    await queryWithClient(client, `
      CREATE TABLE app.courses (
        id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
        canvas_course_id VARCHAR(100) UNIQUE NOT NULL,
        name VARCHAR(255) NOT NULL,
        code VARCHAR(100),
        description TEXT,
        is_active BOOLEAN DEFAULT true,
        term VARCHAR(100),
        start_date DATE,
        end_date DATE,
        enrollment_count INTEGER DEFAULT 0,
        created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
        updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
      );
    `);

    // Create app.user_courses table
    await queryWithClient(client, `
      CREATE TABLE app.user_courses (
        id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
        user_id UUID NOT NULL REFERENCES auth.users(id) ON DELETE CASCADE,
        course_id UUID NOT NULL REFERENCES app.courses(id) ON DELETE CASCADE,
        role VARCHAR(50) DEFAULT 'student' CHECK (role IN ('student', 'teacher', 'ta', 'observer')),
        enrolled_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
        last_accessed_at TIMESTAMP WITH TIME ZONE,
        created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
        updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
        UNIQUE(user_id, course_id)
      );
    `);

    // Create app.files table
    await queryWithClient(client, `
      CREATE TABLE app.files (
        id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
        course_id UUID NOT NULL REFERENCES app.courses(id) ON DELETE CASCADE,
        canvas_file_id VARCHAR(100),
        name VARCHAR(255) NOT NULL,
        original_name VARCHAR(255) NOT NULL,
        mime_type VARCHAR(100) NOT NULL,
        size BIGINT NOT NULL CHECK (size > 0),
        path VARCHAR(500) NOT NULL,
        url TEXT,
        is_processed BOOLEAN DEFAULT false,
        processing_status VARCHAR(20) DEFAULT 'pending' CHECK (processing_status IN ('pending', 'processing', 'completed', 'failed')),
        processing_error TEXT,
        extracted_text TEXT,
        metadata JSONB DEFAULT '{}',
        created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
        updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
      );
    `);

    // Create app.conversations table
    await queryWithClient(client, `
      CREATE TABLE app.conversations (
        id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
        user_id UUID NOT NULL REFERENCES auth.users(id) ON DELETE CASCADE,
        course_id UUID NOT NULL REFERENCES app.courses(id) ON DELETE CASCADE,
        title VARCHAR(255),
        is_active BOOLEAN DEFAULT true,
        message_count INTEGER DEFAULT 0,
        last_message_at TIMESTAMP WITH TIME ZONE,
        created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
        updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
      );
    `);

    // Create app.messages table
    await queryWithClient(client, `
      CREATE TABLE app.messages (
        id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
        conversation_id UUID NOT NULL REFERENCES app.conversations(id) ON DELETE CASCADE,
        role VARCHAR(20) NOT NULL CHECK (role IN ('user', 'assistant', 'system')),
        content TEXT NOT NULL,
        metadata JSONB DEFAULT '{}',
        sources UUID[],
        token_count INTEGER,
        processing_time INTEGER,
        created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
        updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
      );
    `);

    // Create app.flashcards table
    await queryWithClient(client, `
      CREATE TABLE app.flashcards (
        id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
        user_id UUID NOT NULL REFERENCES auth.users(id) ON DELETE CASCADE,
        course_id UUID NOT NULL REFERENCES app.courses(id) ON DELETE CASCADE,
        question TEXT NOT NULL,
        answer TEXT NOT NULL,
        difficulty VARCHAR(10) DEFAULT 'medium' CHECK (difficulty IN ('easy', 'medium', 'hard')),
        tags TEXT[] DEFAULT '{}',
        source_file_ids UUID[] DEFAULT '{}',
        is_active BOOLEAN DEFAULT true,
        review_count INTEGER DEFAULT 0,
        correct_count INTEGER DEFAULT 0,
        last_reviewed_at TIMESTAMP WITH TIME ZONE,
        next_review_at TIMESTAMP WITH TIME ZONE,
        created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
        updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
      );
    `);

    // Create app.exams table
    await queryWithClient(client, `
      CREATE TABLE app.exams (
        id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
        user_id UUID NOT NULL REFERENCES auth.users(id) ON DELETE CASCADE,
        course_id UUID NOT NULL REFERENCES app.courses(id) ON DELETE CASCADE,
        title VARCHAR(255) NOT NULL,
        description TEXT,
        question_count INTEGER NOT NULL CHECK (question_count > 0),
        time_limit INTEGER CHECK (time_limit > 0),
        is_active BOOLEAN DEFAULT true,
        source_file_ids UUID[] DEFAULT '{}',
        settings JSONB DEFAULT '{}',
        created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
        updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
      );
    `);

    // Create app.exam_questions table
    await queryWithClient(client, `
      CREATE TABLE app.exam_questions (
        id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
        exam_id UUID NOT NULL REFERENCES app.exams(id) ON DELETE CASCADE,
        question TEXT NOT NULL,
        type VARCHAR(20) NOT NULL CHECK (type IN ('multiple_choice', 'true_false', 'short_answer', 'essay')),
        options TEXT[],
        correct_answer TEXT NOT NULL,
        explanation TEXT,
        points INTEGER DEFAULT 1 CHECK (points > 0),
        order_index INTEGER NOT NULL CHECK (order_index > 0),
        created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
        updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
      );
    `);

    // Create app.exam_attempts table
    await queryWithClient(client, `
      CREATE TABLE app.exam_attempts (
        id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
        exam_id UUID NOT NULL REFERENCES app.exams(id) ON DELETE CASCADE,
        user_id UUID NOT NULL REFERENCES auth.users(id) ON DELETE CASCADE,
        started_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
        submitted_at TIMESTAMP WITH TIME ZONE,
        score DECIMAL(5,2) CHECK (score >= 0 AND score <= 100),
        answers JSONB DEFAULT '{}',
        time_spent INTEGER,
        is_completed BOOLEAN DEFAULT false,
        created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
        updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
      );
    `);

    // Create app.vector_embeddings table
    await queryWithClient(client, `
      CREATE TABLE app.vector_embeddings (
        id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
        file_id UUID NOT NULL REFERENCES app.files(id) ON DELETE CASCADE,
        chunk_index INTEGER NOT NULL CHECK (chunk_index >= 0),
        content TEXT NOT NULL,
        metadata JSONB DEFAULT '{}',
        token_count INTEGER,
        created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
        updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
        UNIQUE(file_id, chunk_index)
      );
    `);

    // Create analytics.events table
    await queryWithClient(client, `
      CREATE TABLE analytics.events (
        id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
        user_id UUID REFERENCES auth.users(id) ON DELETE SET NULL,
        course_id UUID REFERENCES app.courses(id) ON DELETE SET NULL,
        event_type VARCHAR(100) NOT NULL,
        event_data JSONB DEFAULT '{}',
        session_id VARCHAR(255),
        ip_address INET,
        user_agent TEXT,
        created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
        updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
      );
    `);

    // Create indexes for better performance
    await queryWithClient(client, `
      -- Auth indexes
      CREATE INDEX idx_users_email ON auth.users(email);
      CREATE INDEX idx_users_canvas_id ON auth.users(canvas_user_id);
      CREATE INDEX idx_users_active ON auth.users(is_active);

      CREATE INDEX idx_sessions_user_id ON auth.sessions(user_id);
      CREATE INDEX idx_sessions_expires_at ON auth.sessions(expires_at);
      CREATE INDEX idx_sessions_token_hash ON auth.sessions(token_hash);

      -- App indexes
      CREATE INDEX idx_courses_canvas_id ON app.courses(canvas_course_id);
      CREATE INDEX idx_courses_active ON app.courses(is_active);

      CREATE INDEX idx_user_courses_user_id ON app.user_courses(user_id);
      CREATE INDEX idx_user_courses_course_id ON app.user_courses(course_id);
      CREATE INDEX idx_user_courses_role ON app.user_courses(role);

      CREATE INDEX idx_files_course_id ON app.files(course_id);
      CREATE INDEX idx_files_canvas_id ON app.files(canvas_file_id);
      CREATE INDEX idx_files_processing_status ON app.files(processing_status);
      CREATE INDEX idx_files_is_processed ON app.files(is_processed);

      CREATE INDEX idx_conversations_user_id ON app.conversations(user_id);
      CREATE INDEX idx_conversations_course_id ON app.conversations(course_id);
      CREATE INDEX idx_conversations_active ON app.conversations(is_active);

      CREATE INDEX idx_messages_conversation_id ON app.messages(conversation_id);
      CREATE INDEX idx_messages_role ON app.messages(role);
      CREATE INDEX idx_messages_created_at ON app.messages(created_at);

      CREATE INDEX idx_flashcards_user_id ON app.flashcards(user_id);
      CREATE INDEX idx_flashcards_course_id ON app.flashcards(course_id);
      CREATE INDEX idx_flashcards_active ON app.flashcards(is_active);
      CREATE INDEX idx_flashcards_next_review ON app.flashcards(next_review_at);

      CREATE INDEX idx_exams_user_id ON app.exams(user_id);
      CREATE INDEX idx_exams_course_id ON app.exams(course_id);
      CREATE INDEX idx_exams_active ON app.exams(is_active);

      CREATE INDEX idx_exam_questions_exam_id ON app.exam_questions(exam_id);
      CREATE INDEX idx_exam_questions_order ON app.exam_questions(order_index);

      CREATE INDEX idx_exam_attempts_exam_id ON app.exam_attempts(exam_id);
      CREATE INDEX idx_exam_attempts_user_id ON app.exam_attempts(user_id);
      CREATE INDEX idx_exam_attempts_completed ON app.exam_attempts(is_completed);

      CREATE INDEX idx_vector_embeddings_file_id ON app.vector_embeddings(file_id);
      CREATE INDEX idx_vector_embeddings_chunk_index ON app.vector_embeddings(chunk_index);

      -- Analytics indexes
      CREATE INDEX idx_analytics_events_user_id ON analytics.events(user_id);
      CREATE INDEX idx_analytics_events_course_id ON analytics.events(course_id);
      CREATE INDEX idx_analytics_events_type ON analytics.events(event_type);
      CREATE INDEX idx_analytics_events_created_at ON analytics.events(created_at);
      CREATE INDEX idx_analytics_events_session_id ON analytics.events(session_id);
    `);

    // Create triggers for updated_at columns
    await queryWithClient(client, `
      CREATE TRIGGER update_users_updated_at BEFORE UPDATE ON auth.users
        FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

      CREATE TRIGGER update_sessions_updated_at BEFORE UPDATE ON auth.sessions
        FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

      CREATE TRIGGER update_courses_updated_at BEFORE UPDATE ON app.courses
        FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

      CREATE TRIGGER update_user_courses_updated_at BEFORE UPDATE ON app.user_courses
        FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

      CREATE TRIGGER update_files_updated_at BEFORE UPDATE ON app.files
        FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

      CREATE TRIGGER update_conversations_updated_at BEFORE UPDATE ON app.conversations
        FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

      CREATE TRIGGER update_messages_updated_at BEFORE UPDATE ON app.messages
        FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

      CREATE TRIGGER update_flashcards_updated_at BEFORE UPDATE ON app.flashcards
        FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

      CREATE TRIGGER update_exams_updated_at BEFORE UPDATE ON app.exams
        FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

      CREATE TRIGGER update_exam_questions_updated_at BEFORE UPDATE ON app.exam_questions
        FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

      CREATE TRIGGER update_exam_attempts_updated_at BEFORE UPDATE ON app.exam_attempts
        FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

      CREATE TRIGGER update_vector_embeddings_updated_at BEFORE UPDATE ON app.vector_embeddings
        FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

      CREATE TRIGGER update_analytics_events_updated_at BEFORE UPDATE ON analytics.events
        FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();
    `);

    // Grant permissions
    await queryWithClient(client, `
      GRANT USAGE ON SCHEMA auth TO eightball_user;
      GRANT USAGE ON SCHEMA app TO eightball_user;
      GRANT USAGE ON SCHEMA analytics TO eightball_user;

      GRANT ALL PRIVILEGES ON ALL TABLES IN SCHEMA auth TO eightball_user;
      GRANT ALL PRIVILEGES ON ALL TABLES IN SCHEMA app TO eightball_user;
      GRANT ALL PRIVILEGES ON ALL TABLES IN SCHEMA analytics TO eightball_user;

      GRANT ALL PRIVILEGES ON ALL SEQUENCES IN SCHEMA auth TO eightball_user;
      GRANT ALL PRIVILEGES ON ALL SEQUENCES IN SCHEMA app TO eightball_user;
      GRANT ALL PRIVILEGES ON ALL SEQUENCES IN SCHEMA analytics TO eightball_user;
    `);

    // Insert default admin user
    await queryWithClient(client, `
      INSERT INTO auth.users (email, first_name, last_name, email_verified, is_active)
      VALUES ('<EMAIL>', 'Admin', 'User', true, true)
      ON CONFLICT (email) DO NOTHING;
    `);
  },
  
  async down(client: PoolClient): Promise<void> {
    // Drop tables in reverse order (respecting foreign key constraints)
    await queryWithClient(client, `
      DROP TABLE IF EXISTS analytics.events CASCADE;
      DROP TABLE IF EXISTS app.vector_embeddings CASCADE;
      DROP TABLE IF EXISTS app.exam_attempts CASCADE;
      DROP TABLE IF EXISTS app.exam_questions CASCADE;
      DROP TABLE IF EXISTS app.exams CASCADE;
      DROP TABLE IF EXISTS app.flashcards CASCADE;
      DROP TABLE IF EXISTS app.messages CASCADE;
      DROP TABLE IF EXISTS app.conversations CASCADE;
      DROP TABLE IF EXISTS app.files CASCADE;
      DROP TABLE IF EXISTS app.user_courses CASCADE;
      DROP TABLE IF EXISTS app.courses CASCADE;
      DROP TABLE IF EXISTS auth.sessions CASCADE;
      DROP TABLE IF EXISTS auth.users CASCADE;
    `);

    // Drop function
    await queryWithClient(client, `
      DROP FUNCTION IF EXISTS update_updated_at_column() CASCADE;
    `);

    // Drop schemas
    await queryWithClient(client, `
      DROP SCHEMA IF EXISTS analytics CASCADE;
      DROP SCHEMA IF EXISTS app CASCADE;
      DROP SCHEMA IF EXISTS auth CASCADE;
    `);
  },
};
