# 🎯 Eightball AI

> AI-powered educational assistant for Canvas LMS integration

[![Node.js](https://img.shields.io/badge/Node.js-18+-green.svg)](https://nodejs.org/)
[![TypeScript](https://img.shields.io/badge/TypeScript-5.0+-blue.svg)](https://www.typescriptlang.org/)
[![Docker](https://img.shields.io/badge/Docker-Ready-blue.svg)](https://www.docker.com/)
[![License](https://img.shields.io/badge/License-ISC-yellow.svg)](LICENSE)

## 🚀 Quick Start

### Prerequisites

- **Node.js** 18+ and npm 8+
- **Docker** and Docker Compose
- **Git**

### One-Command Setup

```bash
# Clone the repository
git clone <repository-url>
cd eightball-ai

# Install dependencies and setup development environment
npm install
npm run setup
```

### Start Development

```bash
# Start all services (database + backend + frontend)
npm run dev:all

# Or start services individually
npm run dev:backend  # Backend only
npm run dev:frontend # Frontend only
```

### Access the Application

- **Frontend**: http://localhost:3000
- **Backend API**: http://localhost:3001
- **API Documentation**: http://localhost:3001/api
- **Health Check**: http://localhost:3001/health

## 📋 Available Scripts

### Development
```bash
npm run dev:all      # Start all services with database
npm run dev          # Start backend and frontend only
npm run dev:backend  # Start backend development server
npm run dev:frontend # Start frontend development server
```

### Database
```bash
npm run db:up        # Start PostgreSQL and Redis
npm run db:down      # Stop database services
```

### Docker
```bash
npm run docker:up    # Start all Docker services
npm run docker:down  # Stop all Docker services
npm run docker:build # Build Docker images
npm run docker:clean # Clean up Docker resources
```

### Development Tools
```bash
npm run tools:up     # Start pgAdmin and Redis Commander
npm run tools:down   # Stop development tools
```

### Testing & Quality
```bash
npm run test         # Run all tests
npm run lint         # Run linting
npm run lint:fix     # Fix linting issues
npm run build        # Build for production
```

## 🏗️ Project Structure

```
eightball-ai/
├── backend/                 # Node.js/Express API
│   ├── src/
│   │   ├── controllers/     # Route controllers
│   │   ├── middleware/      # Express middleware
│   │   ├── models/          # Database models
│   │   ├── routes/          # API routes
│   │   ├── services/        # Business logic
│   │   ├── types/           # TypeScript types
│   │   └── utils/           # Utility functions
│   ├── uploads/             # File uploads
│   └── package.json
├── frontend/                # React application
│   ├── src/
│   │   ├── components/      # React components
│   │   ├── pages/           # Page components
│   │   ├── hooks/           # Custom hooks
│   │   ├── services/        # API services
│   │   ├── types/           # TypeScript types
│   │   └── utils/           # Utility functions
│   └── package.json
├── scripts/                 # Development scripts
├── docker-compose.yml       # Docker services
└── package.json            # Root package.json
```

## 🔧 Configuration

### Environment Variables

The setup script automatically creates `.env` files from templates:

- `backend/.env` - Backend configuration
- `frontend/.env` - Frontend configuration

### Docker Services

- **PostgreSQL** (port 5432) - Main database
- **Redis** (port 6379) - Caching and sessions
- **pgAdmin** (port 5050) - Database management UI
- **Redis Commander** (port 8081) - Redis management UI

## 🛠️ Development Tools

### Database Management
```bash
# Access pgAdmin
npm run tools:up
# Visit: http://localhost:5050
# Email: <EMAIL>
# Password: admin123
```

### Redis Management
```bash
# Access Redis Commander
npm run tools:up
# Visit: http://localhost:8081
```

## 📊 Current Implementation Status

| Component | Status | Completion |
|-----------|--------|------------|
| **Environment Setup** | ✅ Complete | 100% |
| **Docker Configuration** | ✅ Complete | 100% |
| **Development Scripts** | ✅ Complete | 100% |
| Backend API | Basic scaffolding | 15% |
| Frontend UI | Basic structure | 15% |
| Database | Not implemented | 0% |
| Authentication | Not implemented | 0% |
| Canvas Integration | Not implemented | 0% |
| AI Features | Not implemented | 0% |

## 🗺️ Next Steps

See [ROADMAP.md](ROADMAP.md) for the complete development plan.

**Immediate priorities:**
1. ✅ Environment & Configuration Setup (Complete)
2. 🔄 Database Setup & Configuration (Next)
3. 🔄 Authentication System
4. 🔄 Basic API Structure

## 🤝 Contributing

1. Fork the repository
2. Create a feature branch: `git checkout -b feature/your-feature`
3. Make your changes
4. Run tests: `npm test`
5. Commit: `git commit -m 'Add your feature'`
6. Push: `git push origin feature/your-feature`
7. Submit a pull request

## 📝 License

This project is licensed under the ISC License - see the [LICENSE](LICENSE) file for details.

## 🆘 Troubleshooting

### Common Issues

**Docker services won't start:**
```bash
# Check Docker is running
docker --version
docker-compose --version

# Clean up and restart
npm run docker:clean
npm run setup
```

**Port conflicts:**
```bash
# Check what's using the ports
lsof -i :3000  # Frontend
lsof -i :3001  # Backend
lsof -i :5432  # PostgreSQL
lsof -i :6379  # Redis
```

**Permission issues:**
```bash
# Make scripts executable
chmod +x scripts/*.sh
```

### Getting Help

- Check the [ROADMAP.md](ROADMAP.md) for development status
- Review Docker logs: `npm run docker:logs`
- Check service health: `docker-compose ps`

---

**Last Updated**: January 2025  
**Version**: 1.0.0  
**Status**: Development Environment Ready ✅
# lauda-ai
