-- ==============================================
-- EIGHTBALL AI - DATABASE INITIALIZATION
-- ==============================================

-- Create extensions
CREATE EXTENSION IF NOT EXISTS "uuid-ossp";
CREATE EXTENSION IF NOT EXISTS "pgcrypto";

-- Create schemas
CREATE SCHEMA IF NOT EXISTS auth;
CREATE SCHEMA IF NOT EXISTS app;
CREATE SCHEMA IF NOT EXISTS analytics;

-- Set search path
SET search_path TO app, auth, public;

-- Create basic tables (will be expanded in migrations)

-- Users table
CREATE TABLE IF NOT EXISTS auth.users (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    email VARCHAR(255) UNIQUE NOT NULL,
    password_hash VARCHAR(255),
    first_name VARCHAR(100),
    last_name VARCHAR(100),
    canvas_user_id VARCHAR(100),
    canvas_access_token TEXT,
    is_active BOOLEAN DEFAULT true,
    email_verified BOOLEAN DEFAULT false,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Sessions table
CREATE TABLE IF NOT EXISTS auth.sessions (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    user_id UUID REFERENCES auth.users(id) ON DELETE CASCADE,
    token_hash VARCHAR(255) NOT NULL,
    expires_at TIMESTAMP WITH TIME ZONE NOT NULL,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Courses table
CREATE TABLE IF NOT EXISTS app.courses (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    canvas_course_id VARCHAR(100) UNIQUE NOT NULL,
    name VARCHAR(255) NOT NULL,
    code VARCHAR(100),
    description TEXT,
    is_active BOOLEAN DEFAULT true,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- User courses relationship
CREATE TABLE IF NOT EXISTS app.user_courses (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    user_id UUID REFERENCES auth.users(id) ON DELETE CASCADE,
    course_id UUID REFERENCES app.courses(id) ON DELETE CASCADE,
    role VARCHAR(50) DEFAULT 'student',
    enrolled_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    UNIQUE(user_id, course_id)
);

-- Create indexes
CREATE INDEX IF NOT EXISTS idx_users_email ON auth.users(email);
CREATE INDEX IF NOT EXISTS idx_users_canvas_id ON auth.users(canvas_user_id);
CREATE INDEX IF NOT EXISTS idx_sessions_user_id ON auth.sessions(user_id);
CREATE INDEX IF NOT EXISTS idx_sessions_expires_at ON auth.sessions(expires_at);
CREATE INDEX IF NOT EXISTS idx_courses_canvas_id ON app.courses(canvas_course_id);
CREATE INDEX IF NOT EXISTS idx_user_courses_user_id ON app.user_courses(user_id);
CREATE INDEX IF NOT EXISTS idx_user_courses_course_id ON app.user_courses(course_id);

-- Create updated_at trigger function
CREATE OR REPLACE FUNCTION update_updated_at_column()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = NOW();
    RETURN NEW;
END;
$$ language 'plpgsql';

-- Apply updated_at triggers
CREATE TRIGGER update_users_updated_at BEFORE UPDATE ON auth.users
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_courses_updated_at BEFORE UPDATE ON app.courses
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

-- Insert default data
INSERT INTO auth.users (email, first_name, last_name, email_verified) 
VALUES ('<EMAIL>', 'Admin', 'User', true)
ON CONFLICT (email) DO NOTHING;

-- Grant permissions
GRANT USAGE ON SCHEMA auth TO eightball_user;
GRANT USAGE ON SCHEMA app TO eightball_user;
GRANT USAGE ON SCHEMA analytics TO eightball_user;

GRANT ALL PRIVILEGES ON ALL TABLES IN SCHEMA auth TO eightball_user;
GRANT ALL PRIVILEGES ON ALL TABLES IN SCHEMA app TO eightball_user;
GRANT ALL PRIVILEGES ON ALL TABLES IN SCHEMA analytics TO eightball_user;

GRANT ALL PRIVILEGES ON ALL SEQUENCES IN SCHEMA auth TO eightball_user;
GRANT ALL PRIVILEGES ON ALL SEQUENCES IN SCHEMA app TO eightball_user;
GRANT ALL PRIVILEGES ON ALL SEQUENCES IN SCHEMA analytics TO eightball_user;
