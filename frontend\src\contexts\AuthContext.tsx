import React, { createContext, useContext, useEffect, useState, ReactNode } from 'react';
import { User } from '../types/user';
import authService, { LoginCredentials, RegisterCredentials } from '../services/auth';

interface AuthContextType {
  user: User | null;
  isAuthenticated: boolean;
  isLoading: boolean;
  login: (credentials: LoginCredentials) => Promise<void>;
  register: (credentials: RegisterCredentials) => Promise<void>;
  logout: () => Promise<void>;
  logoutAll: () => Promise<void>;
  updateProfile: (updates: { firstName?: string; lastName?: string; preferences?: Record<string, any> }) => Promise<void>;
  changePassword: (currentPassword: string, newPassword: string) => Promise<void>;
  refreshUser: () => Promise<void>;
  initiateCanvasOAuth: () => Promise<void>;
  canvasOAuthAvailable: boolean;
}

const AuthContext = createContext<AuthContextType | undefined>(undefined);

interface AuthProviderProps {
  children: ReactNode;
}

export const AuthProvider: React.FC<AuthProviderProps> = ({ children }) => {
  const [user, setUser] = useState<User | null>(null);
  const [isLoading, setIsLoading] = useState(true);
  const [canvasOAuthAvailable, setCanvasOAuthAvailable] = useState(false);

  // Check authentication status on mount
  useEffect(() => {
    checkAuthStatus();
    checkCanvasOAuthStatus();
  }, []);

  const checkAuthStatus = async () => {
    try {
      setIsLoading(true);
      
      if (authService.isAuthenticated()) {
        const currentUser = await authService.getCurrentUser();
        setUser(currentUser);
      }
    } catch (error) {
      console.error('Auth status check failed:', error);
      // Clear invalid token
      authService.clearAccessToken();
      setUser(null);
    } finally {
      setIsLoading(false);
    }
  };

  const checkCanvasOAuthStatus = async () => {
    try {
      const status = await authService.getCanvasOAuthStatus();
      setCanvasOAuthAvailable(status.available);
    } catch (error) {
      console.error('Canvas OAuth status check failed:', error);
      setCanvasOAuthAvailable(false);
    }
  };

  const login = async (credentials: LoginCredentials) => {
    try {
      const response = await authService.login(credentials);
      setUser(response.user);
    } catch (error) {
      throw error;
    }
  };

  const register = async (credentials: RegisterCredentials) => {
    try {
      const response = await authService.register(credentials);
      setUser(response.user);
    } catch (error) {
      throw error;
    }
  };

  const logout = async () => {
    try {
      await authService.logout();
    } catch (error) {
      console.error('Logout failed:', error);
    } finally {
      setUser(null);
    }
  };

  const logoutAll = async () => {
    try {
      await authService.logoutAll();
    } catch (error) {
      console.error('Logout all failed:', error);
    } finally {
      setUser(null);
    }
  };

  const updateProfile = async (updates: { 
    firstName?: string; 
    lastName?: string; 
    preferences?: Record<string, any> 
  }) => {
    try {
      const updatedUser = await authService.updateProfile(updates);
      setUser(updatedUser);
    } catch (error) {
      throw error;
    }
  };

  const changePassword = async (currentPassword: string, newPassword: string) => {
    try {
      await authService.changePassword(currentPassword, newPassword);
      // Password change logs out all sessions, so clear user
      setUser(null);
    } catch (error) {
      throw error;
    }
  };

  const refreshUser = async () => {
    try {
      if (authService.isAuthenticated()) {
        const currentUser = await authService.getCurrentUser();
        setUser(currentUser);
      }
    } catch (error) {
      console.error('User refresh failed:', error);
      setUser(null);
    }
  };

  const initiateCanvasOAuth = async () => {
    try {
      const { authUrl } = await authService.initiateCanvasOAuth();
      // Redirect to Canvas OAuth
      window.location.href = authUrl;
    } catch (error) {
      throw error;
    }
  };

  const value: AuthContextType = {
    user,
    isAuthenticated: !!user,
    isLoading,
    login,
    register,
    logout,
    logoutAll,
    updateProfile,
    changePassword,
    refreshUser,
    initiateCanvasOAuth,
    canvasOAuthAvailable,
  };

  return (
    <AuthContext.Provider value={value}>
      {children}
    </AuthContext.Provider>
  );
};

export const useAuth = (): AuthContextType => {
  const context = useContext(AuthContext);
  if (context === undefined) {
    throw new Error('useAuth must be used within an AuthProvider');
  }
  return context;
};

export default AuthContext;
