import { PoolClient, QueryResult } from 'pg';
import { query, queryWithClient, transaction } from '../database/connection';
import { v4 as uuidv4 } from 'uuid';

/**
 * Base repository class with common CRUD operations
 */
export abstract class BaseRepository<T, CreateT, UpdateT> {
  protected abstract tableName: string;
  protected abstract selectFields: string;

  /**
   * Transform database row to entity
   */
  protected abstract mapRowToEntity(row: any): T;

  /**
   * Transform create input to database fields
   */
  protected abstract mapCreateToRow(input: CreateT): Record<string, any>;

  /**
   * Transform update input to database fields
   */
  protected abstract mapUpdateToRow(input: UpdateT): Record<string, any>;

  /**
   * Find entity by ID
   */
  async findById(id: string): Promise<T | null> {
    const result = await query(
      `SELECT ${this.selectFields} FROM ${this.tableName} WHERE id = $1`,
      [id]
    );

    return result.rows.length > 0 ? this.mapRowToEntity(result.rows[0]) : null;
  }

  /**
   * Find all entities with optional conditions
   */
  async findAll(
    conditions: Record<string, any> = {},
    orderBy: string = 'created_at DESC',
    limit?: number,
    offset?: number
  ): Promise<T[]> {
    let queryText = `SELECT ${this.selectFields} FROM ${this.tableName}`;
    const params: any[] = [];
    let paramIndex = 1;

    // Add WHERE conditions
    if (Object.keys(conditions).length > 0) {
      const whereConditions = Object.keys(conditions).map(key => {
        params.push(conditions[key]);
        return `${key} = $${paramIndex++}`;
      });
      queryText += ` WHERE ${whereConditions.join(' AND ')}`;
    }

    // Add ORDER BY
    queryText += ` ORDER BY ${orderBy}`;

    // Add LIMIT and OFFSET
    if (limit) {
      queryText += ` LIMIT $${paramIndex++}`;
      params.push(limit);
    }
    if (offset) {
      queryText += ` OFFSET $${paramIndex++}`;
      params.push(offset);
    }

    const result = await query(queryText, params);
    return result.rows.map(row => this.mapRowToEntity(row));
  }

  /**
   * Find one entity with conditions
   */
  async findOne(conditions: Record<string, any>): Promise<T | null> {
    const results = await this.findAll(conditions, 'created_at DESC', 1);
    return results.length > 0 ? results[0] : null;
  }

  /**
   * Count entities with optional conditions
   */
  async count(conditions: Record<string, any> = {}): Promise<number> {
    let queryText = `SELECT COUNT(*) as count FROM ${this.tableName}`;
    const params: any[] = [];
    let paramIndex = 1;

    if (Object.keys(conditions).length > 0) {
      const whereConditions = Object.keys(conditions).map(key => {
        params.push(conditions[key]);
        return `${key} = $${paramIndex++}`;
      });
      queryText += ` WHERE ${whereConditions.join(' AND ')}`;
    }

    const result = await query(queryText, params);
    return parseInt(result.rows[0].count);
  }

  /**
   * Create new entity
   */
  async create(input: CreateT): Promise<T> {
    const id = uuidv4();
    const now = new Date();
    const row = this.mapCreateToRow(input);
    
    const fields = ['id', 'created_at', 'updated_at', ...Object.keys(row)];
    const values = [id, now, now, ...Object.values(row)];
    const placeholders = fields.map((_, index) => `$${index + 1}`);

    const queryText = `
      INSERT INTO ${this.tableName} (${fields.join(', ')})
      VALUES (${placeholders.join(', ')})
      RETURNING ${this.selectFields}
    `;

    const result = await query(queryText, values);
    return this.mapRowToEntity(result.rows[0]);
  }

  /**
   * Update entity by ID
   */
  async update(id: string, input: UpdateT): Promise<T | null> {
    const row = this.mapUpdateToRow(input);
    
    if (Object.keys(row).length === 0) {
      // No fields to update, return existing entity
      return this.findById(id);
    }

    const fields = Object.keys(row);
    const values = Object.values(row);
    const setClause = fields.map((field, index) => `${field} = $${index + 2}`);

    const queryText = `
      UPDATE ${this.tableName}
      SET ${setClause.join(', ')}, updated_at = NOW()
      WHERE id = $1
      RETURNING ${this.selectFields}
    `;

    const result = await query(queryText, [id, ...values]);
    return result.rows.length > 0 ? this.mapRowToEntity(result.rows[0]) : null;
  }

  /**
   * Delete entity by ID
   */
  async delete(id: string): Promise<boolean> {
    const result = await query(
      `DELETE FROM ${this.tableName} WHERE id = $1`,
      [id]
    );

    return result.rowCount !== null && result.rowCount > 0;
  }

  /**
   * Soft delete entity by ID (if table has is_active column)
   */
  async softDelete(id: string): Promise<T | null> {
    const result = await query(
      `UPDATE ${this.tableName} SET is_active = false, updated_at = NOW() WHERE id = $1 RETURNING ${this.selectFields}`,
      [id]
    );

    return result.rows.length > 0 ? this.mapRowToEntity(result.rows[0]) : null;
  }

  /**
   * Execute custom query
   */
  protected async executeQuery(queryText: string, params: any[] = []): Promise<QueryResult> {
    return query(queryText, params);
  }

  /**
   * Execute custom query with client (for transactions)
   */
  protected async executeQueryWithClient(
    client: PoolClient,
    queryText: string,
    params: any[] = []
  ): Promise<QueryResult> {
    return queryWithClient(client, queryText, params);
  }

  /**
   * Execute operation in transaction
   */
  protected async executeInTransaction<R>(
    callback: (client: PoolClient) => Promise<R>
  ): Promise<R> {
    return transaction(callback);
  }

  /**
   * Build WHERE clause from conditions
   */
  protected buildWhereClause(
    conditions: Record<string, any>,
    startParamIndex: number = 1
  ): { whereClause: string; params: any[]; nextParamIndex: number } {
    const params: any[] = [];
    let paramIndex = startParamIndex;

    if (Object.keys(conditions).length === 0) {
      return { whereClause: '', params: [], nextParamIndex: paramIndex };
    }

    const whereConditions = Object.keys(conditions).map(key => {
      const value = conditions[key];
      
      if (Array.isArray(value)) {
        // Handle IN clause
        const placeholders = value.map(() => `$${paramIndex++}`);
        params.push(...value);
        return `${key} IN (${placeholders.join(', ')})`;
      } else if (value === null) {
        // Handle NULL check
        return `${key} IS NULL`;
      } else {
        // Handle equality
        params.push(value);
        return `${key} = $${paramIndex++}`;
      }
    });

    return {
      whereClause: `WHERE ${whereConditions.join(' AND ')}`,
      params,
      nextParamIndex: paramIndex,
    };
  }

  /**
   * Convert snake_case to camelCase
   */
  protected toCamelCase(str: string): string {
    return str.replace(/_([a-z])/g, (_, letter) => letter.toUpperCase());
  }

  /**
   * Convert camelCase to snake_case
   */
  protected toSnakeCase(str: string): string {
    return str.replace(/[A-Z]/g, letter => `_${letter.toLowerCase()}`);
  }

  /**
   * Convert object keys from snake_case to camelCase
   */
  protected convertKeysToCamelCase(obj: Record<string, any>): Record<string, any> {
    const result: Record<string, any> = {};
    
    for (const [key, value] of Object.entries(obj)) {
      result[this.toCamelCase(key)] = value;
    }
    
    return result;
  }

  /**
   * Convert object keys from camelCase to snake_case
   */
  protected convertKeysToSnakeCase(obj: Record<string, any>): Record<string, any> {
    const result: Record<string, any> = {};
    
    for (const [key, value] of Object.entries(obj)) {
      result[this.toSnakeCase(key)] = value;
    }
    
    return result;
  }
}
