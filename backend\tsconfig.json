{"compilerOptions": {"target": "ES2020", "module": "commonjs", "lib": ["ES2020"], "outDir": "./dist", "rootDir": "./src", "strict": true, "esModuleInterop": true, "skipLibCheck": true, "forceConsistentCasingInFileNames": true, "resolveJsonModule": true, "declaration": true, "declarationMap": true, "sourceMap": true, "removeComments": true, "noImplicitAny": true, "strictNullChecks": true, "strictFunctionTypes": true, "noImplicitThis": true, "noImplicitReturns": true, "noFallthroughCasesInSwitch": true, "noUncheckedIndexedAccess": true, "exactOptionalPropertyTypes": true, "moduleResolution": "node", "baseUrl": "./", "paths": {"@/*": ["src/*"], "@/types/*": ["src/types/*"], "@/utils/*": ["src/utils/*"], "@/services/*": ["src/services/*"], "@/controllers/*": ["src/controllers/*"], "@/middleware/*": ["src/middleware/*"], "@/models/*": ["src/models/*"]}, "allowSyntheticDefaultImports": true, "experimentalDecorators": true, "emitDecoratorMetadata": true, "incremental": true, "tsBuildInfoFile": "./dist/.tsbuildinfo"}, "include": ["src/**/*", "types/**/*"], "exclude": ["node_modules", "dist", "**/*.test.ts", "**/*.spec.ts", "coverage"], "ts-node": {"require": ["tsconfig-paths/register"]}}