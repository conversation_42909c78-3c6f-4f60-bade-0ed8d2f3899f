#!/bin/bash

# ==============================================
# EIGHTBALL AI - DEVELOPMENT STATUS CHECKER
# ==============================================

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Function to print colored output
print_status() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

print_success() {
    echo -e "${GREEN}[✓]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[!]${NC} $1"
}

print_error() {
    echo -e "${RED}[✗]${NC} $1"
}

# Function to check if command exists
command_exists() {
    command -v "$1" >/dev/null 2>&1
}

# Function to check if file exists
file_exists() {
    [ -f "$1" ]
}

# Function to check if directory exists
dir_exists() {
    [ -d "$1" ]
}

# Function to check if service is running
service_running() {
    docker-compose ps "$1" 2>/dev/null | grep -q "Up"
}

# Function to check if port is open
port_open() {
    nc -z localhost "$1" 2>/dev/null
}

echo "========================================"
echo "🔍 Eightball AI Development Status"
echo "========================================"
echo ""

# Check prerequisites
print_status "Checking prerequisites..."
if command_exists node; then
    NODE_VERSION=$(node --version)
    print_success "Node.js installed: $NODE_VERSION"
else
    print_error "Node.js not installed"
fi

if command_exists npm; then
    NPM_VERSION=$(npm --version)
    print_success "npm installed: $NPM_VERSION"
else
    print_error "npm not installed"
fi

if command_exists docker; then
    DOCKER_VERSION=$(docker --version | cut -d' ' -f3 | cut -d',' -f1)
    print_success "Docker installed: $DOCKER_VERSION"
else
    print_error "Docker not installed"
fi

if command_exists docker-compose; then
    COMPOSE_VERSION=$(docker-compose --version | cut -d' ' -f3 | cut -d',' -f1)
    print_success "Docker Compose installed: $COMPOSE_VERSION"
else
    print_error "Docker Compose not installed"
fi

echo ""

# Check environment files
print_status "Checking environment configuration..."
if file_exists "backend/.env"; then
    print_success "Backend .env file exists"
else
    print_warning "Backend .env file missing (run: cp backend/.env.example backend/.env)"
fi

if file_exists "frontend/.env"; then
    print_success "Frontend .env file exists"
else
    print_warning "Frontend .env file missing (run: cp frontend/.env.example frontend/.env)"
fi

echo ""

# Check dependencies
print_status "Checking dependencies..."
if dir_exists "node_modules"; then
    print_success "Root dependencies installed"
else
    print_warning "Root dependencies not installed (run: npm install)"
fi

if dir_exists "backend/node_modules"; then
    print_success "Backend dependencies installed"
else
    print_warning "Backend dependencies not installed (run: cd backend && npm install)"
fi

if dir_exists "frontend/node_modules"; then
    print_success "Frontend dependencies installed"
else
    print_warning "Frontend dependencies not installed (run: cd frontend && npm install)"
fi

echo ""

# Check Docker services
print_status "Checking Docker services..."
if service_running "postgres"; then
    print_success "PostgreSQL service running"
else
    print_warning "PostgreSQL service not running (run: docker-compose up -d postgres)"
fi

if service_running "redis"; then
    print_success "Redis service running"
else
    print_warning "Redis service not running (run: docker-compose up -d redis)"
fi

echo ""

# Check ports
print_status "Checking service ports..."
if port_open 3000; then
    print_success "Port 3000 (Frontend) is open"
else
    print_warning "Port 3000 (Frontend) is not accessible"
fi

if port_open 3001; then
    print_success "Port 3001 (Backend) is open"
else
    print_warning "Port 3001 (Backend) is not accessible"
fi

if port_open 5432; then
    print_success "Port 5432 (PostgreSQL) is open"
else
    print_warning "Port 5432 (PostgreSQL) is not accessible"
fi

if port_open 6379; then
    print_success "Port 6379 (Redis) is open"
else
    print_warning "Port 6379 (Redis) is not accessible"
fi

echo ""

# Check build status
print_status "Checking build status..."
if dir_exists "backend/dist"; then
    print_success "Backend build exists"
else
    print_warning "Backend not built (run: cd backend && npm run build)"
fi

echo ""

# Summary
print_status "Development environment summary:"
echo ""

# Count status
TOTAL_CHECKS=12
PASSED_CHECKS=0

# Recheck everything for summary
command_exists node && ((PASSED_CHECKS++))
command_exists npm && ((PASSED_CHECKS++))
command_exists docker && ((PASSED_CHECKS++))
command_exists docker-compose && ((PASSED_CHECKS++))
file_exists "backend/.env" && ((PASSED_CHECKS++))
file_exists "frontend/.env" && ((PASSED_CHECKS++))
dir_exists "node_modules" && ((PASSED_CHECKS++))
dir_exists "backend/node_modules" && ((PASSED_CHECKS++))
dir_exists "frontend/node_modules" && ((PASSED_CHECKS++))
service_running "postgres" && ((PASSED_CHECKS++))
service_running "redis" && ((PASSED_CHECKS++))
dir_exists "backend/dist" && ((PASSED_CHECKS++))

PERCENTAGE=$((PASSED_CHECKS * 100 / TOTAL_CHECKS))

if [ $PERCENTAGE -eq 100 ]; then
    print_success "Environment is fully configured! ($PASSED_CHECKS/$TOTAL_CHECKS checks passed)"
    echo ""
    echo "🚀 Ready to start development:"
    echo "   npm run dev:all"
elif [ $PERCENTAGE -ge 75 ]; then
    print_warning "Environment is mostly configured ($PASSED_CHECKS/$TOTAL_CHECKS checks passed)"
    echo ""
    echo "🔧 Run the setup script to complete configuration:"
    echo "   npm run setup"
else
    print_error "Environment needs setup ($PASSED_CHECKS/$TOTAL_CHECKS checks passed)"
    echo ""
    echo "🛠️  Run the setup script:"
    echo "   npm run setup"
fi

echo ""
echo "========================================"
