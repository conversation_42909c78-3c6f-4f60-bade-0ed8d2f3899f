import { BaseRepository } from './base';
import { User, CreateUser, UpdateUser, TABLE_NAMES } from '../types/database';
import bcrypt from 'bcryptjs';

export class UserRepository extends BaseRepository<User, CreateUser, UpdateUser> {
  protected tableName = TABLE_NAMES.USERS;
  protected selectFields = `
    id, email, password_hash, first_name, last_name, canvas_user_id,
    canvas_access_token, is_active, email_verified, last_login_at,
    preferences, created_at, updated_at
  `;

  /**
   * Map database row to User entity
   */
  protected mapRowToEntity(row: any): User {
    return {
      id: row.id,
      email: row.email,
      passwordHash: row.password_hash,
      firstName: row.first_name,
      lastName: row.last_name,
      canvasUserId: row.canvas_user_id,
      canvasAccessToken: row.canvas_access_token,
      isActive: row.is_active,
      emailVerified: row.email_verified,
      lastLoginAt: row.last_login_at,
      preferences: row.preferences || {},
      createdAt: row.created_at,
      updatedAt: row.updated_at,
    };
  }

  /**
   * Map CreateUser input to database row
   */
  protected mapCreateToRow(input: CreateUser): Record<string, any> {
    return {
      email: input.email,
      password_hash: input.passwordHash,
      first_name: input.firstName,
      last_name: input.lastName,
      canvas_user_id: input.canvasUserId,
      canvas_access_token: input.canvasAccessToken,
      is_active: input.isActive ?? true,
      email_verified: input.emailVerified ?? false,
      last_login_at: input.lastLoginAt,
      preferences: input.preferences ? JSON.stringify(input.preferences) : '{}',
    };
  }

  /**
   * Map UpdateUser input to database row
   */
  protected mapUpdateToRow(input: UpdateUser): Record<string, any> {
    const row: Record<string, any> = {};

    if (input.email !== undefined) row.email = input.email;
    if (input.passwordHash !== undefined) row.password_hash = input.passwordHash;
    if (input.firstName !== undefined) row.first_name = input.firstName;
    if (input.lastName !== undefined) row.last_name = input.lastName;
    if (input.canvasUserId !== undefined) row.canvas_user_id = input.canvasUserId;
    if (input.canvasAccessToken !== undefined) row.canvas_access_token = input.canvasAccessToken;
    if (input.isActive !== undefined) row.is_active = input.isActive;
    if (input.emailVerified !== undefined) row.email_verified = input.emailVerified;
    if (input.lastLoginAt !== undefined) row.last_login_at = input.lastLoginAt;
    if (input.preferences !== undefined) row.preferences = JSON.stringify(input.preferences);

    return row;
  }

  /**
   * Find user by email
   */
  async findByEmail(email: string): Promise<User | null> {
    return this.findOne({ email });
  }

  /**
   * Find user by Canvas user ID
   */
  async findByCanvasUserId(canvasUserId: string): Promise<User | null> {
    return this.findOne({ canvas_user_id: canvasUserId });
  }

  /**
   * Create user with hashed password
   */
  async createWithPassword(input: Omit<CreateUser, 'passwordHash'> & { password: string }): Promise<User> {
    const saltRounds = 12;
    const passwordHash = await bcrypt.hash(input.password, saltRounds);

    const { password, ...userInput } = input;
    return this.create({
      ...userInput,
      passwordHash,
    });
  }

  /**
   * Update user password
   */
  async updatePassword(id: string, newPassword: string): Promise<User | null> {
    const saltRounds = 12;
    const passwordHash = await bcrypt.hash(newPassword, saltRounds);

    return this.update(id, { passwordHash });
  }

  /**
   * Verify user password
   */
  async verifyPassword(user: User, password: string): Promise<boolean> {
    if (!user.passwordHash) {
      return false;
    }

    return bcrypt.compare(password, user.passwordHash);
  }

  /**
   * Update last login timestamp
   */
  async updateLastLogin(id: string): Promise<User | null> {
    return this.update(id, { lastLoginAt: new Date() });
  }

  /**
   * Verify user email
   */
  async verifyEmail(id: string): Promise<User | null> {
    return this.update(id, { emailVerified: true });
  }

  /**
   * Update user preferences
   */
  async updatePreferences(id: string, preferences: Record<string, any>): Promise<User | null> {
    return this.update(id, { preferences });
  }

  /**
   * Find active users
   */
  async findActiveUsers(limit?: number, offset?: number): Promise<User[]> {
    return this.findAll({ is_active: true }, 'created_at DESC', limit, offset);
  }

  /**
   * Find users by Canvas course
   */
  async findByCourse(courseId: string): Promise<User[]> {
    const queryText = `
      SELECT ${this.selectFields}
      FROM ${this.tableName} u
      INNER JOIN ${TABLE_NAMES.USER_COURSES} uc ON u.id = uc.user_id
      WHERE uc.course_id = $1 AND u.is_active = true
      ORDER BY u.first_name, u.last_name
    `;

    const result = await this.executeQuery(queryText, [courseId]);
    return result.rows.map(row => this.mapRowToEntity(row));
  }

  /**
   * Search users by name or email
   */
  async searchUsers(searchTerm: string, limit: number = 20): Promise<User[]> {
    const queryText = `
      SELECT ${this.selectFields}
      FROM ${this.tableName}
      WHERE is_active = true
        AND (
          LOWER(first_name) LIKE LOWER($1)
          OR LOWER(last_name) LIKE LOWER($1)
          OR LOWER(email) LIKE LOWER($1)
          OR LOWER(CONCAT(first_name, ' ', last_name)) LIKE LOWER($1)
        )
      ORDER BY first_name, last_name
      LIMIT $2
    `;

    const searchPattern = `%${searchTerm}%`;
    const result = await this.executeQuery(queryText, [searchPattern, limit]);
    return result.rows.map(row => this.mapRowToEntity(row));
  }

  /**
   * Get user statistics
   */
  async getUserStats(userId: string): Promise<{
    totalCourses: number;
    totalConversations: number;
    totalFlashcards: number;
    totalExams: number;
  }> {
    const queryText = `
      SELECT 
        (SELECT COUNT(*) FROM ${TABLE_NAMES.USER_COURSES} WHERE user_id = $1) as total_courses,
        (SELECT COUNT(*) FROM ${TABLE_NAMES.CONVERSATIONS} WHERE user_id = $1) as total_conversations,
        (SELECT COUNT(*) FROM ${TABLE_NAMES.FLASHCARDS} WHERE user_id = $1 AND is_active = true) as total_flashcards,
        (SELECT COUNT(*) FROM ${TABLE_NAMES.EXAMS} WHERE user_id = $1 AND is_active = true) as total_exams
    `;

    const result = await this.executeQuery(queryText, [userId]);
    const row = result.rows[0];

    return {
      totalCourses: parseInt(row.total_courses),
      totalConversations: parseInt(row.total_conversations),
      totalFlashcards: parseInt(row.total_flashcards),
      totalExams: parseInt(row.total_exams),
    };
  }

  /**
   * Deactivate user (soft delete)
   */
  async deactivateUser(id: string): Promise<User | null> {
    return this.update(id, { isActive: false });
  }

  /**
   * Reactivate user
   */
  async reactivateUser(id: string): Promise<User | null> {
    return this.update(id, { isActive: true });
  }
}

// Export singleton instance
export const userRepository = new UserRepository();
