import React from 'react';
import {
  Container,
  Typography,
  Grid,
  Card,
  CardContent,
  CardActions,
  Button,
  Box,
  Chip,
  LinearProgress,
} from '@mui/material';
import {
  School,
  Chat,
  Quiz,
  Assignment,
  TrendingUp,
} from '@mui/icons-material';
import { useNavigate } from 'react-router-dom';

// Mock data - replace with real data from API
const mockCourses = [
  {
    id: '1',
    name: 'Introduction to Computer Science',
    code: 'CS 101',
    instructor: 'Dr<PERSON>',
    progress: 75,
    lastActivity: '2 hours ago',
    totalFiles: 24,
    status: 'active'
  },
  {
    id: '2',
    name: 'Calculus I',
    code: 'MATH 151',
    instructor: '<PERSON><PERSON>',
    progress: 60,
    lastActivity: '1 day ago',
    totalFiles: 18,
    status: 'active'
  },
  {
    id: '3',
    name: 'English Literature',
    code: 'ENG 201',
    instructor: 'Dr. <PERSON>',
    progress: 90,
    lastActivity: '3 days ago',
    totalFiles: 32,
    status: 'completed'
  },
];

const Dashboard: React.FC = () => {
  const navigate = useNavigate();

  const handleCourseAction = (courseId: string, action: string) => {
    switch (action) {
      case 'chat':
        navigate(`/chat/${courseId}`);
        break;
      case 'flashcards':
        navigate(`/flashcards/${courseId}`);
        break;
      case 'exams':
        navigate(`/exams/${courseId}`);
        break;
      default:
        break;
    }
  };

  return (
    <Container maxWidth="lg">
      <Box sx={{ mb: 4 }}>
        <Typography variant="h4" component="h1" gutterBottom>
          Welcome to Eightball AI
        </Typography>
        <Typography variant="subtitle1" color="text.secondary">
          Your AI-powered learning assistant for Canvas courses
        </Typography>
      </Box>

      {/* Quick Stats */}
      <Grid container spacing={3} sx={{ mb: 4 }}>
        <Grid item xs={12} sm={6} md={3}>
          <Card>
            <CardContent>
              <Box sx={{ display: 'flex', alignItems: 'center' }}>
                <School color="primary" sx={{ mr: 2 }} />
                <Box>
                  <Typography variant="h6">{mockCourses.length}</Typography>
                  <Typography variant="body2" color="text.secondary">
                    Active Courses
                  </Typography>
                </Box>
              </Box>
            </CardContent>
          </Card>
        </Grid>
        <Grid item xs={12} sm={6} md={3}>
          <Card>
            <CardContent>
              <Box sx={{ display: 'flex', alignItems: 'center' }}>
                <Chat color="primary" sx={{ mr: 2 }} />
                <Box>
                  <Typography variant="h6">127</Typography>
                  <Typography variant="body2" color="text.secondary">
                    AI Conversations
                  </Typography>
                </Box>
              </Box>
            </CardContent>
          </Card>
        </Grid>
        <Grid item xs={12} sm={6} md={3}>
          <Card>
            <CardContent>
              <Box sx={{ display: 'flex', alignItems: 'center' }}>
                <Quiz color="primary" sx={{ mr: 2 }} />
                <Box>
                  <Typography variant="h6">89</Typography>
                  <Typography variant="body2" color="text.secondary">
                    Flashcards Created
                  </Typography>
                </Box>
              </Box>
            </CardContent>
          </Card>
        </Grid>
        <Grid item xs={12} sm={6} md={3}>
          <Card>
            <CardContent>
              <Box sx={{ display: 'flex', alignItems: 'center' }}>
                <TrendingUp color="primary" sx={{ mr: 2 }} />
                <Box>
                  <Typography variant="h6">92%</Typography>
                  <Typography variant="body2" color="text.secondary">
                    Average Score
                  </Typography>
                </Box>
              </Box>
            </CardContent>
          </Card>
        </Grid>
      </Grid>

      {/* Courses Grid */}
      <Typography variant="h5" component="h2" gutterBottom>
        Your Courses
      </Typography>
      <Grid container spacing={3}>
        {mockCourses.map((course) => (
          <Grid item xs={12} md={6} lg={4} key={course.id}>
            <Card sx={{ height: '100%', display: 'flex', flexDirection: 'column' }}>
              <CardContent sx={{ flexGrow: 1 }}>
                <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'flex-start', mb: 2 }}>
                  <Typography variant="h6" component="h3">
                    {course.name}
                  </Typography>
                  <Chip
                    label={course.status}
                    color={course.status === 'active' ? 'primary' : 'success'}
                    size="small"
                  />
                </Box>
                <Typography variant="body2" color="text.secondary" gutterBottom>
                  {course.code} • {course.instructor}
                </Typography>
                <Typography variant="body2" gutterBottom>
                  {course.totalFiles} files • Last activity: {course.lastActivity}
                </Typography>
                <Box sx={{ mt: 2 }}>
                  <Typography variant="body2" gutterBottom>
                    Progress: {course.progress}%
                  </Typography>
                  <LinearProgress variant="determinate" value={course.progress} />
                </Box>
              </CardContent>
              <CardActions>
                <Button
                  size="small"
                  startIcon={<Chat />}
                  onClick={() => handleCourseAction(course.id, 'chat')}
                >
                  Chat
                </Button>
                <Button
                  size="small"
                  startIcon={<Quiz />}
                  onClick={() => handleCourseAction(course.id, 'flashcards')}
                >
                  Flashcards
                </Button>
                <Button
                  size="small"
                  startIcon={<Assignment />}
                  onClick={() => handleCourseAction(course.id, 'exams')}
                >
                  Exams
                </Button>
              </CardActions>
            </Card>
          </Grid>
        ))}
      </Grid>
    </Container>
  );
};

export default Dashboard;
