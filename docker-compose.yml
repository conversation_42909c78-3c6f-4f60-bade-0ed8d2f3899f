version: '3.8'

services:
  # PostgreSQL Database
  postgres:
    image: postgres:15-alpine
    container_name: eightball-postgres
    restart: unless-stopped
    environment:
      POSTGRES_DB: eightball_ai_dev
      POSTGRES_USER: eightball_user
      POSTGRES_PASSWORD: eightball_password
    ports:
      - "5432:5432"
    volumes:
      - postgres_data:/var/lib/postgresql/data
      - ./backend/database/init:/docker-entrypoint-initdb.d
    networks:
      - eightball-network
    healthcheck:
      test: ["CMD-SHELL", "pg_isready -U eightball_user -d eightball_ai_dev"]
      interval: 30s
      timeout: 10s
      retries: 3

  # Redis Cache
  redis:
    image: redis:7-alpine
    container_name: eightball-redis
    restart: unless-stopped
    ports:
      - "6379:6379"
    volumes:
      - redis_data:/data
    networks:
      - eightball-network
    healthcheck:
      test: ["CMD", "redis-cli", "ping"]
      interval: 30s
      timeout: 10s
      retries: 3

  # Backend API (Development)
  backend:
    build:
      context: ./backend
      dockerfile: Dockerfile
      target: development
    container_name: eightball-backend
    restart: unless-stopped
    environment:
      - NODE_ENV=development
      - DATABASE_HOST=postgres
      - REDIS_HOST=redis
    ports:
      - "3001:3001"
    volumes:
      - ./backend:/app
      - /app/node_modules
      - ./backend/uploads:/app/uploads
    depends_on:
      postgres:
        condition: service_healthy
      redis:
        condition: service_healthy
    networks:
      - eightball-network
    command: npm run dev

  # Frontend (Development)
  frontend:
    build:
      context: ./frontend
      dockerfile: Dockerfile
      target: development
    container_name: eightball-frontend
    restart: unless-stopped
    environment:
      - NODE_ENV=development
      - VITE_API_URL=http://localhost:3001
    ports:
      - "3000:3000"
    volumes:
      - ./frontend:/app
      - /app/node_modules
    depends_on:
      - backend
    networks:
      - eightball-network
    command: npm run dev

  # pgAdmin (Database Management - Optional)
  pgadmin:
    image: dpage/pgadmin4:latest
    container_name: eightball-pgadmin
    restart: unless-stopped
    environment:
      PGADMIN_DEFAULT_EMAIL: <EMAIL>
      PGADMIN_DEFAULT_PASSWORD: admin123
      PGADMIN_CONFIG_SERVER_MODE: 'False'
    ports:
      - "5050:80"
    volumes:
      - pgadmin_data:/var/lib/pgadmin
    depends_on:
      - postgres
    networks:
      - eightball-network
    profiles:
      - tools

  # Redis Commander (Redis Management - Optional)
  redis-commander:
    image: rediscommander/redis-commander:latest
    container_name: eightball-redis-commander
    restart: unless-stopped
    environment:
      - REDIS_HOSTS=local:redis:6379
    ports:
      - "8081:8081"
    depends_on:
      - redis
    networks:
      - eightball-network
    profiles:
      - tools

volumes:
  postgres_data:
    driver: local
  redis_data:
    driver: local
  pgadmin_data:
    driver: local

networks:
  eightball-network:
    driver: bridge
