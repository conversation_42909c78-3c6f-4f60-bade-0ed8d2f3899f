import { z } from 'zod';
import dotenv from 'dotenv';

// Load environment variables
dotenv.config();

// JWT configuration schema
const JWTConfigSchema = z.object({
  secret: z.string().min(32),
  expiresIn: z.string().default('7d'),
  refreshExpiresIn: z.string().default('30d'),
  issuer: z.string().default('eightball-ai'),
  audience: z.string().default('eightball-ai-users'),
});

// Session configuration schema
const SessionConfigSchema = z.object({
  secret: z.string().min(32),
  maxAge: z.number().default(86400000), // 24 hours in milliseconds
  secure: z.boolean().default(false),
  httpOnly: z.boolean().default(true),
  sameSite: z.enum(['strict', 'lax', 'none']).default('lax'),
});

// Canvas OAuth configuration schema
const CanvasOAuthConfigSchema = z.object({
  clientId: z.string(),
  clientSecret: z.string(),
  redirectUri: z.string().url(),
  apiUrl: z.string().url(),
  scopes: z.array(z.string()).default([
    'url:GET|/api/v1/courses',
    'url:GET|/api/v1/courses/:course_id/files',
    'url:GET|/api/v1/files/:id',
    'url:GET|/api/v1/users/:user_id/profile',
  ]),
});

export type JWTConfig = z.infer<typeof JWTConfigSchema>;
export type SessionConfig = z.infer<typeof SessionConfigSchema>;
export type CanvasOAuthConfig = z.infer<typeof CanvasOAuthConfigSchema>;

// Parse and validate JWT configuration
export const jwtConfig: JWTConfig = JWTConfigSchema.parse({
  secret: process.env.JWT_SECRET,
  expiresIn: process.env.JWT_EXPIRES_IN || '7d',
  refreshExpiresIn: process.env.JWT_REFRESH_EXPIRES_IN || '30d',
  issuer: process.env.JWT_ISSUER || 'eightball-ai',
  audience: process.env.JWT_AUDIENCE || 'eightball-ai-users',
});

// Parse and validate session configuration
export const sessionConfig: SessionConfig = SessionConfigSchema.parse({
  secret: process.env.SESSION_SECRET,
  maxAge: parseInt(process.env.SESSION_MAX_AGE || '86400000'),
  secure: process.env.NODE_ENV === 'production',
  httpOnly: true,
  sameSite: process.env.NODE_ENV === 'production' ? 'strict' : 'lax',
});

// Parse and validate Canvas OAuth configuration
export const canvasOAuthConfig: CanvasOAuthConfig | null = (() => {
  try {
    return CanvasOAuthConfigSchema.parse({
      clientId: process.env.CANVAS_CLIENT_ID,
      clientSecret: process.env.CANVAS_CLIENT_SECRET,
      redirectUri: process.env.CANVAS_REDIRECT_URI,
      apiUrl: process.env.CANVAS_API_URL,
      scopes: [
        'url:GET|/api/v1/courses',
        'url:GET|/api/v1/courses/:course_id/files',
        'url:GET|/api/v1/files/:id',
        'url:GET|/api/v1/users/:user_id/profile',
        'url:GET|/api/v1/courses/:course_id/assignments',
        'url:GET|/api/v1/courses/:course_id/modules',
      ],
    });
  } catch (error) {
    console.warn('Canvas OAuth configuration not provided or invalid. Canvas integration will be disabled.');
    return null;
  }
})();

// Export configuration object
export const authConfig = {
  jwt: jwtConfig,
  session: sessionConfig,
  canvas: canvasOAuthConfig,
} as const;

// Authentication constants
export const AUTH_CONSTANTS = {
  COOKIE_NAME: 'eightball_session',
  REFRESH_COOKIE_NAME: 'eightball_refresh',
  HEADER_NAME: 'Authorization',
  BEARER_PREFIX: 'Bearer ',
  SESSION_PREFIX: 'session:',
  REFRESH_PREFIX: 'refresh:',
  PASSWORD_MIN_LENGTH: 8,
  PASSWORD_MAX_LENGTH: 128,
  MAX_LOGIN_ATTEMPTS: 5,
  LOCKOUT_DURATION: 15 * 60 * 1000, // 15 minutes
} as const;
