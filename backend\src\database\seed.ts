#!/usr/bin/env ts-node

import { initializeDatabase, closeConnections } from './connection';
import { userRepository } from '../models/user';

/**
 * Seed development data
 */
async function seedDatabase(): Promise<void> {
  console.log('🌱 Seeding database with development data...');

  try {
    // Initialize database connection
    await initializeDatabase();

    // Create admin user
    const adminEmail = '<EMAIL>';
    const existingAdmin = await userRepository.findByEmail(adminEmail);

    if (!existingAdmin) {
      const admin = await userRepository.createWithPassword({
        email: adminEmail,
        password: 'admin123',
        firstName: 'Admin',
        lastName: 'User',
        emailVerified: true,
        isActive: true,
      });
      console.log(`✅ Created admin user: ${admin.email}`);
    } else {
      console.log(`⚠️ Admin user already exists: ${adminEmail}`);
    }

    // Create test student user
    const studentEmail = '<EMAIL>';
    const existingStudent = await userRepository.findByEmail(studentEmail);

    if (!existingStudent) {
      const student = await userRepository.createWithPassword({
        email: studentEmail,
        password: 'student123',
        firstName: 'Test',
        lastName: 'Student',
        emailVerified: true,
        isActive: true,
      });
      console.log(`✅ Created test student: ${student.email}`);
    } else {
      console.log(`⚠️ Test student already exists: ${studentEmail}`);
    }

    // Create test teacher user
    const teacherEmail = '<EMAIL>';
    const existingTeacher = await userRepository.findByEmail(teacherEmail);

    if (!existingTeacher) {
      const teacher = await userRepository.createWithPassword({
        email: teacherEmail,
        password: 'teacher123',
        firstName: 'Test',
        lastName: 'Teacher',
        emailVerified: true,
        isActive: true,
      });
      console.log(`✅ Created test teacher: ${teacher.email}`);
    } else {
      console.log(`⚠️ Test teacher already exists: ${teacherEmail}`);
    }

    // Create demo users
    const demoUsers = [
      {
        email: '<EMAIL>',
        password: 'demo123',
        firstName: 'Alice',
        lastName: 'Johnson',
      },
      {
        email: '<EMAIL>',
        password: 'demo123',
        firstName: 'Bob',
        lastName: 'Smith',
      },
      {
        email: '<EMAIL>',
        password: 'demo123',
        firstName: 'Carol',
        lastName: 'Davis',
      },
    ];

    for (const userData of demoUsers) {
      const existing = await userRepository.findByEmail(userData.email);
      if (!existing) {
        const user = await userRepository.createWithPassword({
          ...userData,
          emailVerified: true,
          isActive: true,
        });
        console.log(`✅ Created demo user: ${user.email}`);
      } else {
        console.log(`⚠️ Demo user already exists: ${userData.email}`);
      }
    }

    console.log('✅ Database seeding completed successfully!');

  } catch (error) {
    console.error('❌ Database seeding failed:', error);
    process.exit(1);
  } finally {
    await closeConnections();
  }
}

/**
 * Clear all data (for testing)
 */
async function clearDatabase(): Promise<void> {
  console.log('🧹 Clearing database data...');

  try {
    // Initialize database connection
    await initializeDatabase();

    // Clear data in reverse dependency order
    const tables = [
      'analytics.events',
      'app.vector_embeddings',
      'app.exam_attempts',
      'app.exam_questions',
      'app.exams',
      'app.flashcards',
      'app.messages',
      'app.conversations',
      'app.files',
      'app.user_courses',
      'app.courses',
      'auth.sessions',
      'auth.users',
    ];

    for (const table of tables) {
      await userRepository['executeQuery'](`DELETE FROM ${table}`);
      console.log(`✅ Cleared table: ${table}`);
    }

    console.log('✅ Database cleared successfully!');

  } catch (error) {
    console.error('❌ Database clearing failed:', error);
    process.exit(1);
  } finally {
    await closeConnections();
  }
}

/**
 * Reset database (clear + seed)
 */
async function resetDatabase(): Promise<void> {
  console.log('🔄 Resetting database...');
  await clearDatabase();
  await seedDatabase();
}

/**
 * Main function
 */
async function main(): Promise<void> {
  const command = process.argv[2] || 'seed';

  switch (command) {
    case 'seed':
      await seedDatabase();
      break;
    case 'clear':
      await clearDatabase();
      break;
    case 'reset':
      await resetDatabase();
      break;
    default:
      console.log('Usage: npm run db:seed [seed|clear|reset]');
      console.log('  seed  - Add development data (default)');
      console.log('  clear - Remove all data');
      console.log('  reset - Clear and seed data');
      process.exit(1);
  }
}

// Run if called directly
if (require.main === module) {
  main().catch((error) => {
    console.error('❌ Unexpected error:', error);
    process.exit(1);
  });
}
