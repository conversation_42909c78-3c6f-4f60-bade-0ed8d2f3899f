import { userRepository } from '../models/user';
import { generateTokenPair, verifyRefreshToken } from './jwt';
import { createSession, deleteSession, deleteUserSessions, hashToken } from './session';
import { User, CreateUser } from '../types/database';
import { AUTH_CONSTANTS } from './config';
import { z } from 'zod';

// Validation schemas
export const RegisterSchema = z.object({
  email: z.string().email('Invalid email address'),
  password: z.string()
    .min(AUTH_CONSTANTS.PASSWORD_MIN_LENGTH, `Password must be at least ${AUTH_CONSTANTS.PASSWORD_MIN_LENGTH} characters`)
    .max(AUTH_CONSTANTS.PASSWORD_MAX_LENGTH, `Password must be at most ${AUTH_CONSTANTS.PASSWORD_MAX_LENGTH} characters`),
  firstName: z.string().min(1, 'First name is required').max(100, 'First name too long'),
  lastName: z.string().min(1, 'Last name is required').max(100, 'Last name too long'),
});

export const LoginSchema = z.object({
  email: z.string().email('Invalid email address'),
  password: z.string().min(1, 'Password is required'),
});

export const RefreshTokenSchema = z.object({
  refreshToken: z.string().min(1, 'Refresh token is required'),
});

export type RegisterInput = z.infer<typeof RegisterSchema>;
export type LoginInput = z.infer<typeof LoginSchema>;
export type RefreshTokenInput = z.infer<typeof RefreshTokenSchema>;

export interface AuthResult {
  user: Omit<User, 'passwordHash'>;
  accessToken: string;
  refreshToken: string;
  expiresIn: number;
  refreshExpiresIn: number;
}

export interface LoginAttempt {
  email: string;
  attempts: number;
  lastAttempt: Date;
  lockedUntil?: Date;
}

// In-memory store for login attempts (in production, use Redis)
const loginAttempts = new Map<string, LoginAttempt>();

/**
 * Register a new user
 */
export const registerUser = async (
  input: RegisterInput,
  ipAddress?: string,
  userAgent?: string
): Promise<AuthResult> => {
  // Validate input
  const validatedInput = RegisterSchema.parse(input);

  // Check if user already exists
  const existingUser = await userRepository.findByEmail(validatedInput.email);
  if (existingUser) {
    throw new Error('User with this email already exists');
  }

  // Create user
  const user = await userRepository.createWithPassword({
    email: validatedInput.email,
    password: validatedInput.password,
    firstName: validatedInput.firstName,
    lastName: validatedInput.lastName,
    emailVerified: false, // Require email verification
    isActive: true,
  });

  // Generate tokens
  const tokens = generateTokenPair(user);

  // Create session
  const tokenHash = hashToken(tokens.accessToken);
  await createSession(user, tokenHash, ipAddress, userAgent);

  // Return user without password hash
  const { passwordHash, ...userWithoutPassword } = user;

  return {
    user: userWithoutPassword,
    accessToken: tokens.accessToken,
    refreshToken: tokens.refreshToken,
    expiresIn: tokens.expiresIn,
    refreshExpiresIn: tokens.refreshExpiresIn,
  };
};

/**
 * Login user
 */
export const loginUser = async (
  input: LoginInput,
  ipAddress?: string,
  userAgent?: string
): Promise<AuthResult> => {
  // Validate input
  const validatedInput = LoginSchema.parse(input);

  // Check for rate limiting
  await checkLoginAttempts(validatedInput.email);

  // Find user
  const user = await userRepository.findByEmail(validatedInput.email);
  if (!user) {
    await recordFailedLogin(validatedInput.email);
    throw new Error('Invalid email or password');
  }

  // Check if user is active
  if (!user.isActive) {
    throw new Error('User account is deactivated');
  }

  // Verify password
  const isPasswordValid = await userRepository.verifyPassword(user, validatedInput.password);
  if (!isPasswordValid) {
    await recordFailedLogin(validatedInput.email);
    throw new Error('Invalid email or password');
  }

  // Clear failed login attempts
  loginAttempts.delete(validatedInput.email);

  // Update last login
  await userRepository.updateLastLogin(user.id);

  // Generate tokens
  const tokens = generateTokenPair(user);

  // Create session
  const tokenHash = hashToken(tokens.accessToken);
  await createSession(user, tokenHash, ipAddress, userAgent);

  // Return user without password hash
  const { passwordHash, ...userWithoutPassword } = user;

  return {
    user: userWithoutPassword,
    accessToken: tokens.accessToken,
    refreshToken: tokens.refreshToken,
    expiresIn: tokens.expiresIn,
    refreshExpiresIn: tokens.refreshExpiresIn,
  };
};

/**
 * Refresh access token
 */
export const refreshAccessToken = async (
  input: RefreshTokenInput,
  ipAddress?: string,
  userAgent?: string
): Promise<AuthResult> => {
  // Validate input
  const validatedInput = RefreshTokenSchema.parse(input);

  // Verify refresh token
  let payload;
  try {
    payload = verifyRefreshToken(validatedInput.refreshToken);
  } catch (error) {
    throw new Error('Invalid refresh token');
  }

  // Find user
  const user = await userRepository.findById(payload.userId);
  if (!user) {
    throw new Error('User not found');
  }

  // Check if user is active
  if (!user.isActive) {
    throw new Error('User account is deactivated');
  }

  // Generate new tokens
  const tokens = generateTokenPair(user);

  // Create new session
  const tokenHash = hashToken(tokens.accessToken);
  await createSession(user, tokenHash, ipAddress, userAgent);

  // Return user without password hash
  const { passwordHash, ...userWithoutPassword } = user;

  return {
    user: userWithoutPassword,
    accessToken: tokens.accessToken,
    refreshToken: tokens.refreshToken,
    expiresIn: tokens.expiresIn,
    refreshExpiresIn: tokens.refreshExpiresIn,
  };
};

/**
 * Logout user
 */
export const logoutUser = async (sessionId: string): Promise<void> => {
  await deleteSession(sessionId);
};

/**
 * Logout user from all devices
 */
export const logoutUserFromAllDevices = async (userId: string): Promise<void> => {
  await deleteUserSessions(userId);
};

/**
 * Check login attempts and rate limiting
 */
const checkLoginAttempts = async (email: string): Promise<void> => {
  const attempt = loginAttempts.get(email);
  
  if (!attempt) {
    return;
  }

  // Check if account is locked
  if (attempt.lockedUntil && attempt.lockedUntil > new Date()) {
    const remainingTime = Math.ceil((attempt.lockedUntil.getTime() - Date.now()) / 1000 / 60);
    throw new Error(`Account locked. Try again in ${remainingTime} minutes.`);
  }

  // Reset if lock period has expired
  if (attempt.lockedUntil && attempt.lockedUntil <= new Date()) {
    loginAttempts.delete(email);
  }
};

/**
 * Record failed login attempt
 */
const recordFailedLogin = async (email: string): Promise<void> => {
  const now = new Date();
  const attempt = loginAttempts.get(email);

  if (!attempt) {
    loginAttempts.set(email, {
      email,
      attempts: 1,
      lastAttempt: now,
    });
    return;
  }

  attempt.attempts++;
  attempt.lastAttempt = now;

  // Lock account after max attempts
  if (attempt.attempts >= AUTH_CONSTANTS.MAX_LOGIN_ATTEMPTS) {
    attempt.lockedUntil = new Date(now.getTime() + AUTH_CONSTANTS.LOCKOUT_DURATION);
  }

  loginAttempts.set(email, attempt);
};

/**
 * Get current user profile
 */
export const getCurrentUser = async (userId: string): Promise<Omit<User, 'passwordHash'>> => {
  const user = await userRepository.findById(userId);
  if (!user) {
    throw new Error('User not found');
  }

  const { passwordHash, ...userWithoutPassword } = user;
  return userWithoutPassword;
};

/**
 * Update user profile
 */
export const updateUserProfile = async (
  userId: string,
  updates: {
    firstName?: string;
    lastName?: string;
    preferences?: Record<string, any>;
  }
): Promise<Omit<User, 'passwordHash'>> => {
  const updatedUser = await userRepository.update(userId, updates);
  if (!updatedUser) {
    throw new Error('User not found');
  }

  const { passwordHash, ...userWithoutPassword } = updatedUser;
  return userWithoutPassword;
};

/**
 * Change user password
 */
export const changePassword = async (
  userId: string,
  currentPassword: string,
  newPassword: string
): Promise<void> => {
  // Validate new password
  if (newPassword.length < AUTH_CONSTANTS.PASSWORD_MIN_LENGTH) {
    throw new Error(`Password must be at least ${AUTH_CONSTANTS.PASSWORD_MIN_LENGTH} characters`);
  }

  if (newPassword.length > AUTH_CONSTANTS.PASSWORD_MAX_LENGTH) {
    throw new Error(`Password must be at most ${AUTH_CONSTANTS.PASSWORD_MAX_LENGTH} characters`);
  }

  // Get user
  const user = await userRepository.findById(userId);
  if (!user) {
    throw new Error('User not found');
  }

  // Verify current password
  const isCurrentPasswordValid = await userRepository.verifyPassword(user, currentPassword);
  if (!isCurrentPasswordValid) {
    throw new Error('Current password is incorrect');
  }

  // Update password
  await userRepository.updatePassword(userId, newPassword);

  // Logout from all devices for security
  await deleteUserSessions(userId);
};
