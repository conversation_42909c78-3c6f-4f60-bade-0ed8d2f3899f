import { Request, Response, NextFunction } from 'express';
import { verifyAccessToken, extractTokenFromHeader } from '../auth/jwt';
import { getSession, updateSessionActivity, hashToken } from '../auth/session';
import { userRepository } from '../models/user';
import { User } from '../types/database';

// Extend Express Request interface to include user
declare global {
  namespace Express {
    interface Request {
      user?: User;
      sessionId?: string;
    }
  }
}

export interface AuthenticatedRequest extends Request {
  user: User;
  sessionId: string;
}

/**
 * Authentication middleware - requires valid JWT token
 */
export const authenticate = async (
  req: Request,
  res: Response,
  next: NextFunction
): Promise<void> => {
  try {
    // Extract token from Authorization header
    const token = extractTokenFromHeader(req.headers.authorization);
    
    if (!token) {
      res.status(401).json({
        error: 'Unauthorized',
        message: 'No authentication token provided',
      });
      return;
    }

    // Verify JWT token
    let payload;
    try {
      payload = verifyAccessToken(token);
    } catch (error) {
      res.status(401).json({
        error: 'Unauthorized',
        message: error instanceof Error ? error.message : 'Invalid token',
      });
      return;
    }

    // Get user from database
    const user = await userRepository.findById(payload.userId);
    if (!user) {
      res.status(401).json({
        error: 'Unauthorized',
        message: 'User not found',
      });
      return;
    }

    // Check if user is active
    if (!user.isActive) {
      res.status(401).json({
        error: 'Unauthorized',
        message: 'User account is deactivated',
      });
      return;
    }

    // Verify session exists and is valid
    const tokenHash = hashToken(token);
    const session = await getSession(payload.userId);
    
    if (!session || session.tokenHash !== tokenHash) {
      res.status(401).json({
        error: 'Unauthorized',
        message: 'Invalid session',
      });
      return;
    }

    // Update session activity
    await updateSessionActivity(session.id);

    // Attach user and session to request
    req.user = user;
    req.sessionId = session.id;

    next();
  } catch (error) {
    console.error('Authentication middleware error:', error);
    res.status(500).json({
      error: 'Internal Server Error',
      message: 'Authentication failed',
    });
  }
};

/**
 * Optional authentication middleware - doesn't require token but adds user if present
 */
export const optionalAuthenticate = async (
  req: Request,
  res: Response,
  next: NextFunction
): Promise<void> => {
  try {
    const token = extractTokenFromHeader(req.headers.authorization);
    
    if (!token) {
      next();
      return;
    }

    try {
      const payload = verifyAccessToken(token);
      const user = await userRepository.findById(payload.userId);
      
      if (user && user.isActive) {
        const tokenHash = hashToken(token);
        const session = await getSession(payload.userId);
        
        if (session && session.tokenHash === tokenHash) {
          await updateSessionActivity(session.id);
          req.user = user;
          req.sessionId = session.id;
        }
      }
    } catch (error) {
      // Ignore token errors in optional authentication
    }

    next();
  } catch (error) {
    console.error('Optional authentication middleware error:', error);
    next();
  }
};

/**
 * Role-based authorization middleware
 */
export const authorize = (allowedRoles: string[] = []) => {
  return async (req: Request, res: Response, next: NextFunction): Promise<void> => {
    if (!req.user) {
      res.status(401).json({
        error: 'Unauthorized',
        message: 'Authentication required',
      });
      return;
    }

    // For now, we'll implement basic role checking
    // This can be extended when we add role-based access control
    if (allowedRoles.length > 0) {
      // TODO: Implement role checking when user roles are added to the schema
      // For now, allow all authenticated users
    }

    next();
  };
};

/**
 * Admin authorization middleware
 */
export const requireAdmin = async (
  req: Request,
  res: Response,
  next: NextFunction
): Promise<void> => {
  if (!req.user) {
    res.status(401).json({
      error: 'Unauthorized',
      message: 'Authentication required',
    });
    return;
  }

  // Check if user is admin (for now, check email domain or specific emails)
  const adminEmails = ['<EMAIL>'];
  const adminDomains = ['eightball.ai'];
  
  const isAdmin = adminEmails.includes(req.user.email) || 
                  adminDomains.some(domain => req.user.email.endsWith(`@${domain}`));

  if (!isAdmin) {
    res.status(403).json({
      error: 'Forbidden',
      message: 'Admin access required',
    });
    return;
  }

  next();
};

/**
 * Rate limiting by user ID
 */
export const rateLimitByUser = (maxRequests: number = 100, windowMs: number = 15 * 60 * 1000) => {
  const userRequests = new Map<string, { count: number; resetTime: number }>();

  return async (req: Request, res: Response, next: NextFunction): Promise<void> => {
    if (!req.user) {
      next();
      return;
    }

    const userId = req.user.id;
    const now = Date.now();
    const userLimit = userRequests.get(userId);

    if (!userLimit || now > userLimit.resetTime) {
      // Reset or initialize user limit
      userRequests.set(userId, {
        count: 1,
        resetTime: now + windowMs,
      });
      next();
      return;
    }

    if (userLimit.count >= maxRequests) {
      res.status(429).json({
        error: 'Too Many Requests',
        message: 'Rate limit exceeded',
        retryAfter: Math.ceil((userLimit.resetTime - now) / 1000),
      });
      return;
    }

    userLimit.count++;
    next();
  };
};

/**
 * Validate email verification
 */
export const requireEmailVerification = async (
  req: Request,
  res: Response,
  next: NextFunction
): Promise<void> => {
  if (!req.user) {
    res.status(401).json({
      error: 'Unauthorized',
      message: 'Authentication required',
    });
    return;
  }

  if (!req.user.emailVerified) {
    res.status(403).json({
      error: 'Forbidden',
      message: 'Email verification required',
    });
    return;
  }

  next();
};

/**
 * CORS preflight handler for authenticated routes
 */
export const handleAuthCors = (req: Request, res: Response, next: NextFunction): void => {
  if (req.method === 'OPTIONS') {
    res.header('Access-Control-Allow-Methods', 'GET, POST, PUT, DELETE, PATCH');
    res.header('Access-Control-Allow-Headers', 'Content-Type, Authorization');
    res.header('Access-Control-Allow-Credentials', 'true');
    res.status(200).end();
    return;
  }
  next();
};
