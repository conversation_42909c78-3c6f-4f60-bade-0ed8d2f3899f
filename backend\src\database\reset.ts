#!/usr/bin/env ts-node

import { initializeDatabase, closeConnections } from './connection';
import { exec } from 'child_process';
import { promisify } from 'util';

const execAsync = promisify(exec);

/**
 * Reset database completely (migrations + seed)
 */
async function resetDatabase(): Promise<void> {
  console.log('🔄 Resetting database completely...');

  try {
    // Initialize database connection
    await initializeDatabase();

    console.log('📋 Step 1: Rolling back all migrations...');
    try {
      await execAsync('npm run migrate reset', { cwd: process.cwd() });
      console.log('✅ All migrations rolled back');
    } catch (error) {
      console.log('⚠️ No migrations to rollback or rollback failed');
    }

    console.log('📋 Step 2: Running migrations...');
    await execAsync('npm run migrate up', { cwd: process.cwd() });
    console.log('✅ Migrations completed');

    console.log('📋 Step 3: Seeding database...');
    await execAsync('npm run db:seed', { cwd: process.cwd() });
    console.log('✅ Database seeded');

    console.log('🎉 Database reset completed successfully!');
    console.log('');
    console.log('Default users created:');
    console.log('  Admin: <EMAIL> / admin123');
    console.log('  Student: <EMAIL> / student123');
    console.log('  Teacher: <EMAIL> / teacher123');
    console.log('');

  } catch (error) {
    console.error('❌ Database reset failed:', error);
    process.exit(1);
  } finally {
    await closeConnections();
  }
}

/**
 * Quick reset (clear data + seed, keep schema)
 */
async function quickReset(): Promise<void> {
  console.log('⚡ Quick database reset (keeping schema)...');

  try {
    // Initialize database connection
    await initializeDatabase();

    console.log('📋 Step 1: Clearing data...');
    await execAsync('npm run db:seed clear', { cwd: process.cwd() });
    console.log('✅ Data cleared');

    console.log('📋 Step 2: Seeding database...');
    await execAsync('npm run db:seed', { cwd: process.cwd() });
    console.log('✅ Database seeded');

    console.log('🎉 Quick reset completed successfully!');

  } catch (error) {
    console.error('❌ Quick reset failed:', error);
    process.exit(1);
  } finally {
    await closeConnections();
  }
}

/**
 * Check database status
 */
async function checkStatus(): Promise<void> {
  console.log('📊 Checking database status...');

  try {
    // Initialize database connection
    await initializeDatabase();

    console.log('📋 Migration status:');
    await execAsync('npm run migrate status', { cwd: process.cwd() });

    console.log('📋 Database connection: ✅ Connected');

  } catch (error) {
    console.error('❌ Database status check failed:', error);
    process.exit(1);
  } finally {
    await closeConnections();
  }
}

/**
 * Main function
 */
async function main(): Promise<void> {
  const command = process.argv[2] || 'full';

  switch (command) {
    case 'full':
      await resetDatabase();
      break;
    case 'quick':
      await quickReset();
      break;
    case 'status':
      await checkStatus();
      break;
    default:
      console.log('Usage: npm run db:reset [full|quick|status]');
      console.log('  full   - Complete reset (migrations + seed) [default]');
      console.log('  quick  - Quick reset (clear data + seed, keep schema)');
      console.log('  status - Check database status');
      process.exit(1);
  }
}

// Run if called directly
if (require.main === module) {
  main().catch((error) => {
    console.error('❌ Unexpected error:', error);
    process.exit(1);
  });
}
