import { getRedis } from '../database/connection';
import { AUTH_CONSTANTS } from './config';
import { User, Session, CreateSession } from '../types/database';
import { userRepository } from '../models/user';
import { v4 as uuidv4 } from 'uuid';
import crypto from 'crypto';

export interface SessionData {
  id: string;
  userId: string;
  tokenHash: string;
  expiresAt: Date;
  ipAddress?: string;
  userAgent?: string;
  createdAt: Date;
}

export interface ActiveSession {
  sessionId: string;
  userId: string;
  email: string;
  firstName: string;
  lastName: string;
  lastActivity: Date;
  ipAddress?: string;
  userAgent?: string;
}

/**
 * Create a new session
 */
export const createSession = async (
  user: User,
  tokenHash: string,
  ipAddress?: string,
  userAgent?: string
): Promise<SessionData> => {
  const redis = getRedis();
  const sessionId = uuidv4();
  const expiresAt = new Date(Date.now() + AUTH_CONSTANTS.LOCKOUT_DURATION);

  const sessionData: SessionData = {
    id: sessionId,
    userId: user.id,
    tokenHash,
    expiresAt,
    ipAddress,
    userAgent,
    createdAt: new Date(),
  };

  // Store in Redis with expiration
  const sessionKey = `${AUTH_CONSTANTS.SESSION_PREFIX}${sessionId}`;
  await redis.setEx(
    sessionKey,
    Math.floor((expiresAt.getTime() - Date.now()) / 1000),
    JSON.stringify(sessionData)
  );

  // Also store a user sessions index
  const userSessionsKey = `user_sessions:${user.id}`;
  await redis.sAdd(userSessionsKey, sessionId);
  await redis.expire(userSessionsKey, Math.floor((expiresAt.getTime() - Date.now()) / 1000));

  return sessionData;
};

/**
 * Get session by ID
 */
export const getSession = async (sessionId: string): Promise<SessionData | null> => {
  const redis = getRedis();
  const sessionKey = `${AUTH_CONSTANTS.SESSION_PREFIX}${sessionId}`;
  
  const sessionDataStr = await redis.get(sessionKey);
  if (!sessionDataStr) {
    return null;
  }

  try {
    const sessionData = JSON.parse(sessionDataStr) as SessionData;
    
    // Check if session is expired
    if (new Date(sessionData.expiresAt) < new Date()) {
      await deleteSession(sessionId);
      return null;
    }

    return sessionData;
  } catch (error) {
    console.error('Error parsing session data:', error);
    await deleteSession(sessionId);
    return null;
  }
};

/**
 * Update session activity
 */
export const updateSessionActivity = async (sessionId: string): Promise<void> => {
  const redis = getRedis();
  const sessionKey = `${AUTH_CONSTANTS.SESSION_PREFIX}${sessionId}`;
  
  const sessionData = await getSession(sessionId);
  if (!sessionData) {
    return;
  }

  // Update last activity timestamp
  const updatedSessionData = {
    ...sessionData,
    lastActivity: new Date(),
  };

  // Extend expiration
  const newExpiresAt = new Date(Date.now() + AUTH_CONSTANTS.LOCKOUT_DURATION);
  updatedSessionData.expiresAt = newExpiresAt;

  await redis.setEx(
    sessionKey,
    Math.floor((newExpiresAt.getTime() - Date.now()) / 1000),
    JSON.stringify(updatedSessionData)
  );
};

/**
 * Delete session
 */
export const deleteSession = async (sessionId: string): Promise<void> => {
  const redis = getRedis();
  const sessionKey = `${AUTH_CONSTANTS.SESSION_PREFIX}${sessionId}`;
  
  // Get session data to find user ID
  const sessionData = await getSession(sessionId);
  
  // Delete session
  await redis.del(sessionKey);
  
  // Remove from user sessions index
  if (sessionData) {
    const userSessionsKey = `user_sessions:${sessionData.userId}`;
    await redis.sRem(userSessionsKey, sessionId);
  }
};

/**
 * Delete all sessions for a user
 */
export const deleteUserSessions = async (userId: string): Promise<void> => {
  const redis = getRedis();
  const userSessionsKey = `user_sessions:${userId}`;
  
  // Get all session IDs for the user
  const sessionIds = await redis.sMembers(userSessionsKey);
  
  // Delete each session
  const deletePromises = sessionIds.map(sessionId => {
    const sessionKey = `${AUTH_CONSTANTS.SESSION_PREFIX}${sessionId}`;
    return redis.del(sessionKey);
  });
  
  await Promise.all(deletePromises);
  
  // Delete the user sessions index
  await redis.del(userSessionsKey);
};

/**
 * Get all active sessions for a user
 */
export const getUserSessions = async (userId: string): Promise<ActiveSession[]> => {
  const redis = getRedis();
  const userSessionsKey = `user_sessions:${userId}`;
  
  const sessionIds = await redis.sMembers(userSessionsKey);
  const sessions: ActiveSession[] = [];
  
  for (const sessionId of sessionIds) {
    const sessionData = await getSession(sessionId);
    if (sessionData) {
      const user = await userRepository.findById(sessionData.userId);
      if (user) {
        sessions.push({
          sessionId: sessionData.id,
          userId: user.id,
          email: user.email,
          firstName: user.firstName,
          lastName: user.lastName,
          lastActivity: new Date(sessionData.createdAt),
          ipAddress: sessionData.ipAddress,
          userAgent: sessionData.userAgent,
        });
      }
    }
  }
  
  return sessions;
};

/**
 * Clean up expired sessions
 */
export const cleanupExpiredSessions = async (): Promise<number> => {
  const redis = getRedis();
  let cleanedCount = 0;
  
  // Get all session keys
  const sessionKeys = await redis.keys(`${AUTH_CONSTANTS.SESSION_PREFIX}*`);
  
  for (const sessionKey of sessionKeys) {
    const sessionDataStr = await redis.get(sessionKey);
    if (sessionDataStr) {
      try {
        const sessionData = JSON.parse(sessionDataStr) as SessionData;
        if (new Date(sessionData.expiresAt) < new Date()) {
          const sessionId = sessionKey.replace(AUTH_CONSTANTS.SESSION_PREFIX, '');
          await deleteSession(sessionId);
          cleanedCount++;
        }
      } catch (error) {
        // Invalid session data, delete it
        await redis.del(sessionKey);
        cleanedCount++;
      }
    }
  }
  
  return cleanedCount;
};

/**
 * Hash token for storage
 */
export const hashToken = (token: string): string => {
  return crypto.createHash('sha256').update(token).digest('hex');
};

/**
 * Verify token hash
 */
export const verifyTokenHash = (token: string, hash: string): boolean => {
  const tokenHash = hashToken(token);
  return crypto.timingSafeEqual(Buffer.from(tokenHash), Buffer.from(hash));
};

/**
 * Get session statistics
 */
export const getSessionStats = async (): Promise<{
  totalActiveSessions: number;
  uniqueUsers: number;
  sessionsPerUser: Record<string, number>;
}> => {
  const redis = getRedis();
  const sessionKeys = await redis.keys(`${AUTH_CONSTANTS.SESSION_PREFIX}*`);
  
  const userSessionCounts: Record<string, number> = {};
  let validSessions = 0;
  
  for (const sessionKey of sessionKeys) {
    const sessionDataStr = await redis.get(sessionKey);
    if (sessionDataStr) {
      try {
        const sessionData = JSON.parse(sessionDataStr) as SessionData;
        if (new Date(sessionData.expiresAt) >= new Date()) {
          validSessions++;
          userSessionCounts[sessionData.userId] = (userSessionCounts[sessionData.userId] || 0) + 1;
        }
      } catch (error) {
        // Invalid session data, ignore
      }
    }
  }
  
  return {
    totalActiveSessions: validSessions,
    uniqueUsers: Object.keys(userSessionCounts).length,
    sessionsPerUser: userSessionCounts,
  };
};
