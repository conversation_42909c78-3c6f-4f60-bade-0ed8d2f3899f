{"name": "eightball-ai-backend", "version": "1.0.0", "description": "Backend for Eightball AI application", "main": "dist/index.js", "scripts": {"dev": "nodemon src/index.ts", "build": "tsc", "start": "node dist/index.js", "test": "jest", "test:watch": "jest --watch", "lint": "eslint src/**/*.ts", "lint:fix": "eslint src/**/*.ts --fix", "migrate": "ts-node src/database/migrate.ts", "migrate:up": "ts-node src/database/migrate.ts up", "migrate:down": "ts-node src/database/migrate.ts down", "migrate:create": "ts-node src/database/create-migration.ts", "db:seed": "ts-node src/database/seed.ts", "db:reset": "ts-node src/database/reset.ts"}, "keywords": ["ai", "education", "canvas", "lms"], "author": "zander-brown", "license": "ISC", "dependencies": {"bcryptjs": "^3.0.2", "cors": "^2.8.5", "dotenv": "^16.5.0", "express": "^5.1.0", "express-rate-limit": "^7.5.0", "helmet": "^8.1.0", "jsonwebtoken": "^9.0.2", "morgan": "^1.10.0", "pg": "^8.12.0", "redis": "^4.7.0", "zod": "^3.23.8", "@pinecone-database/pinecone": "^3.0.3", "uuid": "^10.0.0"}, "devDependencies": {"@types/bcryptjs": "^2.4.6", "@types/cors": "^2.8.19", "@types/express": "^5.0.3", "@types/jest": "^29.5.14", "@types/jsonwebtoken": "^9.0.9", "@types/morgan": "^1.9.10", "@types/node": "^22.15.30", "@types/pg": "^8.11.10", "@types/uuid": "^10.0.0", "@typescript-eslint/eslint-plugin": "^8.33.1", "@typescript-eslint/parser": "^8.33.1", "eslint": "^9.28.0", "jest": "^29.7.0", "nodemon": "^3.1.10", "ts-node": "^10.9.2", "typescript": "^5.8.3", "tsconfig-paths": "^4.2.0"}}