import { User } from '../types/user';

const API_BASE_URL = import.meta.env.VITE_API_URL || 'http://localhost:3001';

export interface LoginCredentials {
  email: string;
  password: string;
}

export interface RegisterCredentials {
  email: string;
  password: string;
  firstName: string;
  lastName: string;
}

export interface AuthResponse {
  user: User;
  accessToken: string;
  expiresIn: number;
  message: string;
}

export interface ApiError {
  error: string;
  message: string;
}

class AuthService {
  private accessToken: string | null = null;

  constructor() {
    // Load token from localStorage on initialization
    this.accessToken = localStorage.getItem('accessToken');
  }

  /**
   * Set access token
   */
  setAccessToken(token: string): void {
    this.accessToken = token;
    localStorage.setItem('accessToken', token);
  }

  /**
   * Get access token
   */
  getAccessToken(): string | null {
    return this.accessToken;
  }

  /**
   * Clear access token
   */
  clearAccessToken(): void {
    this.accessToken = null;
    localStorage.removeItem('accessToken');
  }

  /**
   * Get authorization headers
   */
  getAuthHeaders(): Record<string, string> {
    const headers: Record<string, string> = {
      'Content-Type': 'application/json',
    };

    if (this.accessToken) {
      headers.Authorization = `Bearer ${this.accessToken}`;
    }

    return headers;
  }

  /**
   * Make authenticated API request
   */
  private async apiRequest<T>(
    endpoint: string,
    options: RequestInit = {}
  ): Promise<T> {
    const url = `${API_BASE_URL}${endpoint}`;
    const config: RequestInit = {
      ...options,
      headers: {
        ...this.getAuthHeaders(),
        ...options.headers,
      },
      credentials: 'include', // Include cookies for refresh token
    };

    const response = await fetch(url, config);

    if (!response.ok) {
      const errorData: ApiError = await response.json().catch(() => ({
        error: 'Network Error',
        message: 'Failed to connect to server',
      }));
      throw new Error(errorData.message || 'Request failed');
    }

    return response.json();
  }

  /**
   * Register new user
   */
  async register(credentials: RegisterCredentials): Promise<AuthResponse> {
    const response = await this.apiRequest<AuthResponse>('/api/auth/register', {
      method: 'POST',
      body: JSON.stringify(credentials),
    });

    this.setAccessToken(response.accessToken);
    return response;
  }

  /**
   * Login user
   */
  async login(credentials: LoginCredentials): Promise<AuthResponse> {
    const response = await this.apiRequest<AuthResponse>('/api/auth/login', {
      method: 'POST',
      body: JSON.stringify(credentials),
    });

    this.setAccessToken(response.accessToken);
    return response;
  }

  /**
   * Logout user
   */
  async logout(): Promise<void> {
    try {
      await this.apiRequest('/api/auth/logout', {
        method: 'POST',
      });
    } catch (error) {
      // Continue with logout even if API call fails
      console.warn('Logout API call failed:', error);
    } finally {
      this.clearAccessToken();
    }
  }

  /**
   * Logout from all devices
   */
  async logoutAll(): Promise<void> {
    try {
      await this.apiRequest('/api/auth/logout-all', {
        method: 'POST',
      });
    } catch (error) {
      console.warn('Logout all API call failed:', error);
    } finally {
      this.clearAccessToken();
    }
  }

  /**
   * Get current user
   */
  async getCurrentUser(): Promise<User> {
    const response = await this.apiRequest<{ user: User }>('/api/auth/me');
    return response.user;
  }

  /**
   * Update user profile
   */
  async updateProfile(updates: {
    firstName?: string;
    lastName?: string;
    preferences?: Record<string, any>;
  }): Promise<User> {
    const response = await this.apiRequest<{ user: User }>('/api/auth/profile', {
      method: 'PUT',
      body: JSON.stringify(updates),
    });
    return response.user;
  }

  /**
   * Change password
   */
  async changePassword(currentPassword: string, newPassword: string): Promise<void> {
    await this.apiRequest('/api/auth/password', {
      method: 'PUT',
      body: JSON.stringify({ currentPassword, newPassword }),
    });
  }

  /**
   * Refresh access token
   */
  async refreshToken(): Promise<AuthResponse> {
    const response = await this.apiRequest<AuthResponse>('/api/auth/refresh', {
      method: 'POST',
    });

    this.setAccessToken(response.accessToken);
    return response;
  }

  /**
   * Check authentication status
   */
  async checkAuthStatus(): Promise<{ authenticated: boolean; user?: User }> {
    try {
      const response = await this.apiRequest<{ authenticated: boolean; user: User }>('/api/auth/status');
      return response;
    } catch (error) {
      return { authenticated: false };
    }
  }

  /**
   * Initiate Canvas OAuth
   */
  async initiateCanvasOAuth(): Promise<{ authUrl: string; state: string }> {
    return this.apiRequest<{ authUrl: string; state: string }>('/api/auth/canvas');
  }

  /**
   * Check Canvas OAuth status
   */
  async getCanvasOAuthStatus(): Promise<{ configured: boolean; available: boolean }> {
    return this.apiRequest<{ configured: boolean; available: boolean }>('/api/auth/canvas/status');
  }

  /**
   * Check if user is authenticated
   */
  isAuthenticated(): boolean {
    return !!this.accessToken;
  }

  /**
   * Auto-refresh token when it's about to expire
   */
  async autoRefreshToken(): Promise<void> {
    if (!this.accessToken) {
      return;
    }

    try {
      // Try to refresh the token
      await this.refreshToken();
    } catch (error) {
      // If refresh fails, clear the token
      this.clearAccessToken();
      throw error;
    }
  }

  /**
   * Setup automatic token refresh
   */
  setupAutoRefresh(): void {
    // Refresh token every 30 minutes
    setInterval(() => {
      if (this.isAuthenticated()) {
        this.autoRefreshToken().catch(() => {
          // Token refresh failed, user will need to login again
        });
      }
    }, 30 * 60 * 1000); // 30 minutes
  }
}

// Create singleton instance
export const authService = new AuthService();

// Setup auto-refresh on module load
authService.setupAutoRefresh();

export default authService;
