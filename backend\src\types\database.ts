import { z } from 'zod';

// Base entity schema with common fields
export const BaseEntitySchema = z.object({
  id: z.string().uuid(),
  createdAt: z.date(),
  updatedAt: z.date(),
});

// User entity
export const UserSchema = BaseEntitySchema.extend({
  email: z.string().email(),
  passwordHash: z.string().optional(),
  firstName: z.string().min(1).max(100),
  lastName: z.string().min(1).max(100),
  canvasUserId: z.string().optional(),
  canvasAccessToken: z.string().optional(),
  isActive: z.boolean().default(true),
  emailVerified: z.boolean().default(false),
  lastLoginAt: z.date().optional(),
  preferences: z.record(z.any()).optional(),
});

// Session entity
export const SessionSchema = BaseEntitySchema.extend({
  userId: z.string().uuid(),
  tokenHash: z.string(),
  expiresAt: z.date(),
  ipAddress: z.string().optional(),
  userAgent: z.string().optional(),
});

// Course entity
export const CourseSchema = BaseEntitySchema.extend({
  canvasCourseId: z.string(),
  name: z.string().min(1).max(255),
  code: z.string().max(100).optional(),
  description: z.string().optional(),
  isActive: z.boolean().default(true),
  term: z.string().optional(),
  startDate: z.date().optional(),
  endDate: z.date().optional(),
  enrollmentCount: z.number().default(0),
});

// User-Course relationship
export const UserCourseSchema = BaseEntitySchema.extend({
  userId: z.string().uuid(),
  courseId: z.string().uuid(),
  role: z.enum(['student', 'teacher', 'ta', 'observer']).default('student'),
  enrolledAt: z.date(),
  lastAccessedAt: z.date().optional(),
});

// File entity
export const FileSchema = BaseEntitySchema.extend({
  courseId: z.string().uuid(),
  canvasFileId: z.string().optional(),
  name: z.string().min(1).max(255),
  originalName: z.string(),
  mimeType: z.string(),
  size: z.number().positive(),
  path: z.string(),
  url: z.string().url().optional(),
  isProcessed: z.boolean().default(false),
  processingStatus: z.enum(['pending', 'processing', 'completed', 'failed']).default('pending'),
  processingError: z.string().optional(),
  extractedText: z.string().optional(),
  metadata: z.record(z.any()).optional(),
});

// Conversation entity
export const ConversationSchema = BaseEntitySchema.extend({
  userId: z.string().uuid(),
  courseId: z.string().uuid(),
  title: z.string().max(255).optional(),
  isActive: z.boolean().default(true),
  messageCount: z.number().default(0),
  lastMessageAt: z.date().optional(),
});

// Message entity
export const MessageSchema = BaseEntitySchema.extend({
  conversationId: z.string().uuid(),
  role: z.enum(['user', 'assistant', 'system']),
  content: z.string(),
  metadata: z.record(z.any()).optional(),
  sources: z.array(z.string().uuid()).optional(), // File IDs used as sources
  tokenCount: z.number().optional(),
  processingTime: z.number().optional(), // milliseconds
});

// Flashcard entity
export const FlashcardSchema = BaseEntitySchema.extend({
  userId: z.string().uuid(),
  courseId: z.string().uuid(),
  question: z.string(),
  answer: z.string(),
  difficulty: z.enum(['easy', 'medium', 'hard']).default('medium'),
  tags: z.array(z.string()).default([]),
  sourceFileIds: z.array(z.string().uuid()).default([]),
  isActive: z.boolean().default(true),
  reviewCount: z.number().default(0),
  correctCount: z.number().default(0),
  lastReviewedAt: z.date().optional(),
  nextReviewAt: z.date().optional(),
});

// Exam entity
export const ExamSchema = BaseEntitySchema.extend({
  userId: z.string().uuid(),
  courseId: z.string().uuid(),
  title: z.string().min(1).max(255),
  description: z.string().optional(),
  questionCount: z.number().positive(),
  timeLimit: z.number().positive().optional(), // minutes
  isActive: z.boolean().default(true),
  sourceFileIds: z.array(z.string().uuid()).default([]),
  settings: z.record(z.any()).optional(),
});

// Exam Question entity
export const ExamQuestionSchema = BaseEntitySchema.extend({
  examId: z.string().uuid(),
  question: z.string(),
  type: z.enum(['multiple_choice', 'true_false', 'short_answer', 'essay']),
  options: z.array(z.string()).optional(), // For multiple choice
  correctAnswer: z.string(),
  explanation: z.string().optional(),
  points: z.number().positive().default(1),
  order: z.number().positive(),
});

// Exam Attempt entity
export const ExamAttemptSchema = BaseEntitySchema.extend({
  examId: z.string().uuid(),
  userId: z.string().uuid(),
  startedAt: z.date(),
  submittedAt: z.date().optional(),
  score: z.number().min(0).max(100).optional(),
  answers: z.record(z.string()).optional(), // questionId -> answer
  timeSpent: z.number().optional(), // minutes
  isCompleted: z.boolean().default(false),
});

// Vector Embedding entity
export const VectorEmbeddingSchema = BaseEntitySchema.extend({
  fileId: z.string().uuid(),
  chunkIndex: z.number().nonnegative(),
  content: z.string(),
  embedding: z.array(z.number()).optional(), // Will be stored in vector DB
  metadata: z.record(z.any()).optional(),
  tokenCount: z.number().optional(),
});

// Analytics Event entity
export const AnalyticsEventSchema = BaseEntitySchema.extend({
  userId: z.string().uuid(),
  courseId: z.string().uuid().optional(),
  eventType: z.string(),
  eventData: z.record(z.any()).optional(),
  sessionId: z.string().optional(),
  ipAddress: z.string().optional(),
  userAgent: z.string().optional(),
});

// Type exports
export type User = z.infer<typeof UserSchema>;
export type Session = z.infer<typeof SessionSchema>;
export type Course = z.infer<typeof CourseSchema>;
export type UserCourse = z.infer<typeof UserCourseSchema>;
export type File = z.infer<typeof FileSchema>;
export type Conversation = z.infer<typeof ConversationSchema>;
export type Message = z.infer<typeof MessageSchema>;
export type Flashcard = z.infer<typeof FlashcardSchema>;
export type Exam = z.infer<typeof ExamSchema>;
export type ExamQuestion = z.infer<typeof ExamQuestionSchema>;
export type ExamAttempt = z.infer<typeof ExamAttemptSchema>;
export type VectorEmbedding = z.infer<typeof VectorEmbeddingSchema>;
export type AnalyticsEvent = z.infer<typeof AnalyticsEventSchema>;

// Database table names
export const TABLE_NAMES = {
  USERS: 'auth.users',
  SESSIONS: 'auth.sessions',
  COURSES: 'app.courses',
  USER_COURSES: 'app.user_courses',
  FILES: 'app.files',
  CONVERSATIONS: 'app.conversations',
  MESSAGES: 'app.messages',
  FLASHCARDS: 'app.flashcards',
  EXAMS: 'app.exams',
  EXAM_QUESTIONS: 'app.exam_questions',
  EXAM_ATTEMPTS: 'app.exam_attempts',
  VECTOR_EMBEDDINGS: 'app.vector_embeddings',
  ANALYTICS_EVENTS: 'analytics.events',
  MIGRATIONS: 'public.migrations',
} as const;

// Create input types (without id, createdAt, updatedAt)
export type CreateUser = Omit<User, 'id' | 'createdAt' | 'updatedAt'>;
export type CreateSession = Omit<Session, 'id' | 'createdAt' | 'updatedAt'>;
export type CreateCourse = Omit<Course, 'id' | 'createdAt' | 'updatedAt'>;
export type CreateUserCourse = Omit<UserCourse, 'id' | 'createdAt' | 'updatedAt'>;
export type CreateFile = Omit<File, 'id' | 'createdAt' | 'updatedAt'>;
export type CreateConversation = Omit<Conversation, 'id' | 'createdAt' | 'updatedAt'>;
export type CreateMessage = Omit<Message, 'id' | 'createdAt' | 'updatedAt'>;
export type CreateFlashcard = Omit<Flashcard, 'id' | 'createdAt' | 'updatedAt'>;
export type CreateExam = Omit<Exam, 'id' | 'createdAt' | 'updatedAt'>;
export type CreateExamQuestion = Omit<ExamQuestion, 'id' | 'createdAt' | 'updatedAt'>;
export type CreateExamAttempt = Omit<ExamAttempt, 'id' | 'createdAt' | 'updatedAt'>;
export type CreateVectorEmbedding = Omit<VectorEmbedding, 'id' | 'createdAt' | 'updatedAt'>;
export type CreateAnalyticsEvent = Omit<AnalyticsEvent, 'id' | 'createdAt' | 'updatedAt'>;

// Update input types (partial, without id, createdAt, updatedAt)
export type UpdateUser = Partial<Omit<User, 'id' | 'createdAt' | 'updatedAt'>>;
export type UpdateCourse = Partial<Omit<Course, 'id' | 'createdAt' | 'updatedAt'>>;
export type UpdateFile = Partial<Omit<File, 'id' | 'createdAt' | 'updatedAt'>>;
export type UpdateConversation = Partial<Omit<Conversation, 'id' | 'createdAt' | 'updatedAt'>>;
export type UpdateFlashcard = Partial<Omit<Flashcard, 'id' | 'createdAt' | 'updatedAt'>>;
export type UpdateExam = Partial<Omit<Exam, 'id' | 'createdAt' | 'updatedAt'>>;
export type UpdateExamQuestion = Partial<Omit<ExamQuestion, 'id' | 'createdAt' | 'updatedAt'>>;
export type UpdateExamAttempt = Partial<Omit<ExamAttempt, 'id' | 'createdAt' | 'updatedAt'>>;
