import { Router, Request, Response } from 'express';
import { canvasOAuthConfig } from './config';
import { userRepository } from '../models/user';
import { generateTokenPair } from './jwt';
import { createSession, hashToken } from './session';
import { AUTH_CONSTANTS } from './config';
import crypto from 'crypto';

const router = Router();

// Store OAuth state for CSRF protection
const oauthStates = new Map<string, { state: string; createdAt: Date }>();

// Clean up expired states every hour
setInterval(() => {
  const oneHourAgo = new Date(Date.now() - 60 * 60 * 1000);
  for (const [key, value] of oauthStates.entries()) {
    if (value.createdAt < oneHourAgo) {
      oauthStates.delete(key);
    }
  }
}, 60 * 60 * 1000);

/**
 * Canvas OAuth configuration interface
 */
interface CanvasTokenResponse {
  access_token: string;
  token_type: string;
  user: {
    id: number;
    name: string;
    email: string;
    login_id: string;
  };
  refresh_token?: string;
  expires_in?: number;
}

interface CanvasUserProfile {
  id: number;
  name: string;
  email: string;
  login_id: string;
  first_name?: string;
  last_name?: string;
  avatar_url?: string;
}

/**
 * GET /api/auth/canvas
 * Initiate Canvas OAuth flow
 */
router.get('/canvas', (req: Request, res: Response): void => {
  if (!canvasOAuthConfig) {
    res.status(503).json({
      error: 'Service Unavailable',
      message: 'Canvas OAuth is not configured',
    });
    return;
  }

  try {
    // Generate state for CSRF protection
    const state = crypto.randomBytes(32).toString('hex');
    oauthStates.set(state, { state, createdAt: new Date() });

    // Build authorization URL
    const authUrl = new URL('/login/oauth2/auth', canvasOAuthConfig.apiUrl.replace('/api/v1', ''));
    authUrl.searchParams.set('client_id', canvasOAuthConfig.clientId);
    authUrl.searchParams.set('response_type', 'code');
    authUrl.searchParams.set('redirect_uri', canvasOAuthConfig.redirectUri);
    authUrl.searchParams.set('scope', canvasOAuthConfig.scopes.join(' '));
    authUrl.searchParams.set('state', state);

    res.json({
      authUrl: authUrl.toString(),
      state,
    });
  } catch (error) {
    console.error('Canvas OAuth initiation error:', error);
    res.status(500).json({
      error: 'OAuth Initiation Failed',
      message: 'Failed to initiate Canvas OAuth flow',
    });
  }
});

/**
 * GET /api/auth/canvas/callback
 * Handle Canvas OAuth callback
 */
router.get('/canvas/callback', async (req: Request, res: Response): Promise<void> => {
  if (!canvasOAuthConfig) {
    res.status(503).json({
      error: 'Service Unavailable',
      message: 'Canvas OAuth is not configured',
    });
    return;
  }

  try {
    const { code, state, error } = req.query;

    // Check for OAuth error
    if (error) {
      res.status(400).json({
        error: 'OAuth Error',
        message: `Canvas OAuth error: ${error}`,
      });
      return;
    }

    // Validate required parameters
    if (!code || !state) {
      res.status(400).json({
        error: 'Bad Request',
        message: 'Missing authorization code or state',
      });
      return;
    }

    // Verify state for CSRF protection
    const storedState = oauthStates.get(state as string);
    if (!storedState) {
      res.status(400).json({
        error: 'Bad Request',
        message: 'Invalid or expired state parameter',
      });
      return;
    }

    // Clean up used state
    oauthStates.delete(state as string);

    // Exchange code for access token
    const tokenResponse = await exchangeCodeForToken(code as string);
    
    // Get user profile from Canvas
    const canvasUser = await getCanvasUserProfile(tokenResponse.access_token);

    // Find or create user in our database
    let user = await userRepository.findByCanvasUserId(canvasUser.id.toString());
    
    if (!user) {
      // Check if user exists by email
      user = await userRepository.findByEmail(canvasUser.email);
      
      if (user) {
        // Link existing user to Canvas account
        await userRepository.update(user.id, {
          canvasUserId: canvasUser.id.toString(),
          canvasAccessToken: tokenResponse.access_token,
        });
      } else {
        // Create new user
        const [firstName, lastName] = parseCanvasName(canvasUser.name);
        user = await userRepository.create({
          email: canvasUser.email,
          firstName: firstName || canvasUser.first_name || 'Canvas',
          lastName: lastName || canvasUser.last_name || 'User',
          canvasUserId: canvasUser.id.toString(),
          canvasAccessToken: tokenResponse.access_token,
          emailVerified: true, // Trust Canvas email verification
          isActive: true,
        });
      }
    } else {
      // Update existing Canvas user
      await userRepository.update(user.id, {
        canvasAccessToken: tokenResponse.access_token,
        lastLoginAt: new Date(),
      });
    }

    // Generate our JWT tokens
    const tokens = generateTokenPair(user);

    // Create session
    const tokenHash = hashToken(tokens.accessToken);
    await createSession(user, tokenHash, req.ip, req.get('User-Agent'));

    // Set refresh token as HTTP-only cookie
    res.cookie(AUTH_CONSTANTS.REFRESH_COOKIE_NAME, tokens.refreshToken, {
      httpOnly: true,
      secure: process.env.NODE_ENV === 'production',
      sameSite: 'lax',
      maxAge: tokens.refreshExpiresIn * 1000,
    });

    // Redirect to frontend with access token
    const frontendUrl = process.env.CORS_ORIGIN || 'http://localhost:3000';
    const redirectUrl = new URL('/auth/canvas/success', frontendUrl);
    redirectUrl.searchParams.set('token', tokens.accessToken);
    redirectUrl.searchParams.set('expires', tokens.expiresIn.toString());

    res.redirect(redirectUrl.toString());
  } catch (error) {
    console.error('Canvas OAuth callback error:', error);
    
    // Redirect to frontend with error
    const frontendUrl = process.env.CORS_ORIGIN || 'http://localhost:3000';
    const redirectUrl = new URL('/auth/canvas/error', frontendUrl);
    redirectUrl.searchParams.set('error', 'oauth_failed');
    redirectUrl.searchParams.set('message', error instanceof Error ? error.message : 'OAuth failed');

    res.redirect(redirectUrl.toString());
  }
});

/**
 * Exchange authorization code for access token
 */
async function exchangeCodeForToken(code: string): Promise<CanvasTokenResponse> {
  if (!canvasOAuthConfig) {
    throw new Error('Canvas OAuth not configured');
  }

  const tokenUrl = new URL('/login/oauth2/token', canvasOAuthConfig.apiUrl.replace('/api/v1', ''));
  
  const response = await fetch(tokenUrl.toString(), {
    method: 'POST',
    headers: {
      'Content-Type': 'application/x-www-form-urlencoded',
    },
    body: new URLSearchParams({
      grant_type: 'authorization_code',
      client_id: canvasOAuthConfig.clientId,
      client_secret: canvasOAuthConfig.clientSecret,
      redirect_uri: canvasOAuthConfig.redirectUri,
      code,
    }),
  });

  if (!response.ok) {
    const errorText = await response.text();
    throw new Error(`Token exchange failed: ${response.status} ${errorText}`);
  }

  return response.json() as Promise<CanvasTokenResponse>;
}

/**
 * Get user profile from Canvas API
 */
async function getCanvasUserProfile(accessToken: string): Promise<CanvasUserProfile> {
  if (!canvasOAuthConfig) {
    throw new Error('Canvas OAuth not configured');
  }

  const profileUrl = new URL('/api/v1/users/self/profile', canvasOAuthConfig.apiUrl);
  
  const response = await fetch(profileUrl.toString(), {
    headers: {
      'Authorization': `Bearer ${accessToken}`,
    },
  });

  if (!response.ok) {
    const errorText = await response.text();
    throw new Error(`Profile fetch failed: ${response.status} ${errorText}`);
  }

  return response.json() as Promise<CanvasUserProfile>;
}

/**
 * Parse Canvas display name into first and last name
 */
function parseCanvasName(displayName: string): [string?, string?] {
  if (!displayName) {
    return [undefined, undefined];
  }

  const parts = displayName.trim().split(/\s+/);
  if (parts.length === 1) {
    return [parts[0], undefined];
  } else if (parts.length === 2) {
    return [parts[0], parts[1]];
  } else {
    // More than 2 parts, assume first is first name, rest is last name
    return [parts[0], parts.slice(1).join(' ')];
  }
}

/**
 * GET /api/auth/canvas/status
 * Check Canvas OAuth configuration status
 */
router.get('/canvas/status', (req: Request, res: Response): void => {
  res.json({
    configured: !!canvasOAuthConfig,
    available: !!canvasOAuthConfig,
  });
});

export default router;
